<?php
/**
 * Classe per la gestione del database delle varianti prodotto
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

class CPV_Database {
    
    /**
     * Nome della tabella delle varianti
     */
    private $table_name;
    
    /**
     * Versione del database
     */
    private $db_version = '1.0.0';
    
    /**
     * Costruttore
     */
    public function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'cpv_product_variants';

        // Hook per aggiornamenti del database
        add_action('plugins_loaded', array($this, 'check_db_version'));
    }

    /**
     * Restituisce il nome della tabella
     */
    public function get_table_name() {
        return $this->table_name;
    }
    
    /**
     * Crea le tabelle del database
     */
    public function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // SQL per creare la tabella delle varianti
        $sql = "CREATE TABLE {$this->table_name} (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            product_id bigint(20) unsigned NOT NULL,
            variant_name varchar(255) NOT NULL,
            variant_image varchar(500) DEFAULT NULL,
            variant_price decimal(10,2) NOT NULL,
            sort_order int(11) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY product_id (product_id),
            KEY is_active (is_active),
            KEY sort_order (sort_order)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        $result = dbDelta($sql);

        // Debug: Log del risultato della creazione tabella
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV Database Creation Result: ' . print_r($result, true));
        }

        // Verifica se la tabella è stata creata
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'");
        if (!$table_exists) {
            error_log('CPV Error: Failed to create table ' . $this->table_name);
        }

        // Salva la versione del database
        update_option('cpv_db_version', $this->db_version);
    }
    
    /**
     * Controlla la versione del database e aggiorna se necessario
     */
    public function check_db_version() {
        $installed_version = get_option('cpv_db_version', '0');
        
        if (version_compare($installed_version, $this->db_version, '<')) {
            $this->create_tables();
        }
    }
    
    /**
     * Aggiunge una nuova variante
     */
    public function add_variant($product_id, $variant_name, $variant_image, $variant_price, $sort_order = 0) {
        global $wpdb;

        // Debug: Log dei parametri
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV add_variant called with: product_id=' . $product_id . ', name=' . $variant_name . ', price=' . $variant_price . ', image=' . $variant_image);
        }

        // Prepara i dati
        $data = array(
            'product_id' => intval($product_id),
            'variant_name' => sanitize_text_field($variant_name),
            'variant_image' => esc_url_raw($variant_image),
            'variant_price' => floatval($variant_price),
            'sort_order' => intval($sort_order),
            'is_active' => 1
        );

        $formats = array('%d', '%s', '%s', '%f', '%d', '%d');

        // Debug: Log dei dati preparati
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV insert data: ' . print_r($data, true));
            error_log('CPV table name: ' . $this->table_name);
        }

        $result = $wpdb->insert($this->table_name, $data, $formats);

        // Debug: Log del risultato
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV insert result: ' . ($result === false ? 'FALSE' : $result));
            if ($result === false && $wpdb->last_error) {
                error_log('CPV insert error: ' . $wpdb->last_error);
            }
            error_log('CPV insert_id: ' . $wpdb->insert_id);
        }

        if ($result === false) {
            $error_message = $wpdb->last_error ? $wpdb->last_error : __('Errore durante l\'inserimento della variante nel database.', 'custom-product-variants');
            return new WP_Error('db_error', $error_message);
        }

        return $wpdb->insert_id;
    }
    
    /**
     * Aggiorna una variante esistente
     */
    public function update_variant($variant_id, $variant_name, $variant_image, $variant_price, $sort_order = 0) {
        global $wpdb;
        
        $result = $wpdb->update(
            $this->table_name,
            array(
                'variant_name' => sanitize_text_field($variant_name),
                'variant_image' => esc_url_raw($variant_image),
                'variant_price' => floatval($variant_price),
                'sort_order' => intval($sort_order)
            ),
            array('id' => intval($variant_id)),
            array('%s', '%s', '%f', '%d'),
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Errore durante l\'aggiornamento della variante.', 'custom-product-variants'));
        }
        
        return true;
    }
    
    /**
     * Elimina una variante
     */
    public function delete_variant($variant_id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->table_name,
            array('id' => intval($variant_id)),
            array('%d')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', __('Errore durante l\'eliminazione della variante.', 'custom-product-variants'));
        }
        
        return true;
    }
    
    /**
     * Ottiene tutte le varianti di un prodotto
     */
    public function get_product_variants($product_id, $active_only = true) {
        global $wpdb;
        
        $where_clause = $active_only ? 'AND is_active = 1' : '';
        
        $results = $wpdb->get_results(
            $wpdb->prepare(
                "SELECT * FROM {$this->table_name} 
                WHERE product_id = %d {$where_clause} 
                ORDER BY sort_order ASC, id ASC",
                intval($product_id)
            )
        );
        
        return $results ? $results : array();
    }
    
    /**
     * Ottiene una singola variante per ID
     */
    public function get_variant($variant_id) {
        global $wpdb;
        
        $result = $wpdb->get_row(
            $wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                intval($variant_id)
            )
        );
        
        return $result;
    }
    
    /**
     * Conta le varianti di un prodotto
     */
    public function count_product_variants($product_id, $active_only = true) {
        global $wpdb;
        
        $where_clause = $active_only ? 'AND is_active = 1' : '';
        
        $count = $wpdb->get_var(
            $wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} 
                WHERE product_id = %d {$where_clause}",
                intval($product_id)
            )
        );
        
        return intval($count);
    }
    
    /**
     * Attiva o disattiva una variante
     */
    public function toggle_variant_status($variant_id, $is_active = 1) {
        global $wpdb;
        
        $result = $wpdb->update(
            $this->table_name,
            array('is_active' => intval($is_active)),
            array('id' => intval($variant_id)),
            array('%d'),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Aggiorna l'ordine delle varianti
     */
    public function update_variants_order($variants_order) {
        global $wpdb;
        
        if (!is_array($variants_order)) {
            return false;
        }
        
        foreach ($variants_order as $order => $variant_id) {
            $wpdb->update(
                $this->table_name,
                array('sort_order' => intval($order)),
                array('id' => intval($variant_id)),
                array('%d'),
                array('%d')
            );
        }
        
        return true;
    }
    
    /**
     * Elimina tutte le varianti di un prodotto
     */
    public function delete_product_variants($product_id) {
        global $wpdb;
        
        $result = $wpdb->delete(
            $this->table_name,
            array('product_id' => intval($product_id)),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Ottiene statistiche sulle varianti
     */
    public function get_variants_stats() {
        global $wpdb;

        $stats = $wpdb->get_row(
            "SELECT
                COUNT(*) as total_variants,
                COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_variants,
                COUNT(DISTINCT product_id) as products_with_variants
            FROM {$this->table_name}"
        );

        return $stats;
    }

    /**
     * Verifica se la tabella esiste
     */
    public function table_exists() {
        global $wpdb;

        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'");
        return !empty($table_exists);
    }

    /**
     * Debug: Ottiene informazioni sulla tabella
     */
    public function get_table_info() {
        global $wpdb;

        if (!$this->table_exists()) {
            return array('error' => 'Table does not exist');
        }

        $columns = $wpdb->get_results("DESCRIBE {$this->table_name}");
        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");

        return array(
            'table_name' => $this->table_name,
            'exists' => true,
            'columns' => $columns,
            'row_count' => $count
        );
    }
}
