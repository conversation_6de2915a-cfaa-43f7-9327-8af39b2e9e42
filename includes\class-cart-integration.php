<?php
/**
 * Classe per l'integrazione con il carrello WooCommerce
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

class CPV_Cart_Integration {
    
    /**
     * Istanza della classe Database
     */
    private $database;
    
    /**
     * Costruttore
     */
    public function __construct() {
        $this->database = new CPV_Database();
        $this->init_hooks();
    }
    
    /**
     * Inizializza gli hook per l'integrazione con il carrello
     */
    private function init_hooks() {
        // Hook per intercettare l'aggiunta al carrello
        add_filter('woocommerce_add_to_cart_validation', array($this, 'validate_add_to_cart'), 10, 3);
        add_filter('woocommerce_add_cart_item_data', array($this, 'add_cart_item_data'), 10, 3);
        
        // Hook per modificare i dati del prodotto nel carrello
        add_filter('woocommerce_get_cart_item_from_session', array($this, 'get_cart_item_from_session'), 10, 3);
        add_action('woocommerce_before_calculate_totals', array($this, 'before_calculate_totals'));
        
        // Hook per visualizzare le informazioni della variante nel carrello
        add_filter('woocommerce_cart_item_name', array($this, 'cart_item_name'), 10, 3);
        add_filter('woocommerce_cart_item_thumbnail', array($this, 'cart_item_thumbnail'), 10, 3);
        
        // Hook per l'ordine
        add_action('woocommerce_checkout_create_order_line_item', array($this, 'checkout_create_order_line_item'), 10, 4);
        add_filter('woocommerce_order_item_name', array($this, 'order_item_name'), 10, 2);
        
        // Hook per visualizzare le informazioni della variante nell'email
        add_filter('woocommerce_order_item_display_meta_key', array($this, 'order_item_display_meta_key'), 10, 3);
        add_filter('woocommerce_order_item_display_meta_value', array($this, 'order_item_display_meta_value'), 10, 3);
    }
    
    /**
     * Valida l'aggiunta al carrello quando è selezionata una variante
     */
    public function validate_add_to_cart($passed, $product_id, $quantity) {
        // Se non è stata selezionata una variante, procedi normalmente
        if (!isset($_POST['cpv_selected_variant']) || empty($_POST['cpv_selected_variant'])) {
            return $passed;
        }
        
        $variant_id = intval($_POST['cpv_selected_variant']);
        $variant = $this->database->get_variant($variant_id);
        
        // Verifica che la variante esista e sia attiva
        if (!$variant || !$variant->is_active) {
            wc_add_notice(__('La variante selezionata non è disponibile.', 'custom-product-variants'), 'error');
            return false;
        }
        
        // Verifica che la variante appartenga al prodotto corretto
        if ($variant->product_id != $product_id) {
            wc_add_notice(__('Variante non valida per questo prodotto.', 'custom-product-variants'), 'error');
            return false;
        }
        
        return $passed;
    }
    
    /**
     * Aggiunge i dati della variante al carrello
     */
    public function add_cart_item_data($cart_item_data, $product_id, $variation_id) {
        // Se è stata selezionata una variante, aggiungila ai dati del carrello
        if (isset($_POST['cpv_selected_variant']) && !empty($_POST['cpv_selected_variant'])) {
            $variant_id = intval($_POST['cpv_selected_variant']);
            $variant = $this->database->get_variant($variant_id);
            
            if ($variant && $variant->is_active && $variant->product_id == $product_id) {
                $cart_item_data['cpv_variant'] = array(
                    'variant_id' => $variant->id,
                    'variant_name' => $variant->variant_name,
                    'variant_price' => $variant->variant_price,
                    'variant_image' => $variant->variant_image
                );
                
                // Crea un hash unico per questo item del carrello
                $cart_item_data['unique_key'] = md5(microtime() . rand());
            }
        }
        
        return $cart_item_data;
    }
    
    /**
     * Ripristina i dati della variante dalla sessione
     */
    public function get_cart_item_from_session($item, $values, $key) {
        if (array_key_exists('cpv_variant', $values)) {
            $item['cpv_variant'] = $values['cpv_variant'];
        }
        
        return $item;
    }
    
    /**
     * Modifica il prezzo del prodotto nel carrello se è selezionata una variante
     */
    public function before_calculate_totals($cart) {
        if (is_admin() && !defined('DOING_AJAX')) {
            return;
        }
        
        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            if (isset($cart_item['cpv_variant'])) {
                $variant_data = $cart_item['cpv_variant'];
                $cart_item['data']->set_price($variant_data['variant_price']);
            }
        }
    }
    
    /**
     * Modifica il nome del prodotto nel carrello per includere la variante
     */
    public function cart_item_name($product_name, $cart_item, $cart_item_key) {
        if (isset($cart_item['cpv_variant'])) {
            $variant_data = $cart_item['cpv_variant'];
            $product_name .= '<br><small class="cpv-variant-info">' . 
                           sprintf(__('Variante: %s', 'custom-product-variants'), esc_html($variant_data['variant_name'])) . 
                           '</small>';
        }
        
        return $product_name;
    }
    
    /**
     * Modifica l'immagine del prodotto nel carrello se la variante ha un'immagine
     */
    public function cart_item_thumbnail($product_image, $cart_item, $cart_item_key) {
        if (isset($cart_item['cpv_variant']) && !empty($cart_item['cpv_variant']['variant_image'])) {
            $variant_data = $cart_item['cpv_variant'];
            $product = $cart_item['data'];
            
            $image_size = apply_filters('single_product_archive_thumbnail_size', 'woocommerce_thumbnail');
            $product_image = sprintf(
                '<img src="%s" alt="%s" class="attachment-woocommerce_thumbnail size-woocommerce_thumbnail">',
                esc_url($variant_data['variant_image']),
                esc_attr($variant_data['variant_name'])
            );
        }
        
        return $product_image;
    }
    
    /**
     * Aggiunge i metadati della variante all'ordine
     */
    public function checkout_create_order_line_item($item, $cart_item_key, $values, $order) {
        if (isset($values['cpv_variant'])) {
            $variant_data = $values['cpv_variant'];
            
            $item->add_meta_data(__('Variante', 'custom-product-variants'), $variant_data['variant_name']);
            $item->add_meta_data('_cpv_variant_id', $variant_data['variant_id']);
            $item->add_meta_data('_cpv_variant_name', $variant_data['variant_name']);
            $item->add_meta_data('_cpv_variant_price', $variant_data['variant_price']);
            
            if (!empty($variant_data['variant_image'])) {
                $item->add_meta_data('_cpv_variant_image', $variant_data['variant_image']);
            }
        }
    }
    
    /**
     * Modifica il nome del prodotto nell'ordine per includere la variante
     */
    public function order_item_name($item_name, $item) {
        if ($item->get_meta('_cpv_variant_name')) {
            $variant_name = $item->get_meta('_cpv_variant_name');
            $item_name .= ' - ' . sprintf(__('Variante: %s', 'custom-product-variants'), $variant_name);
        }
        
        return $item_name;
    }
    
    /**
     * Personalizza la visualizzazione delle chiavi dei metadati nell'ordine
     */
    public function order_item_display_meta_key($display_key, $meta, $item) {
        if ($meta->key === '_cpv_variant_name') {
            return __('Variante', 'custom-product-variants');
        }
        
        return $display_key;
    }
    
    /**
     * Personalizza la visualizzazione dei valori dei metadati nell'ordine
     */
    public function order_item_display_meta_value($display_value, $meta, $item) {
        if ($meta->key === '_cpv_variant_name') {
            return esc_html($meta->value);
        }
        
        return $display_value;
    }
    
    /**
     * Verifica se un item del carrello ha una variante
     */
    public function cart_item_has_variant($cart_item) {
        return isset($cart_item['cpv_variant']);
    }
    
    /**
     * Ottiene i dati della variante da un item del carrello
     */
    public function get_cart_item_variant_data($cart_item) {
        if ($this->cart_item_has_variant($cart_item)) {
            return $cart_item['cpv_variant'];
        }
        
        return null;
    }
    
    /**
     * Ottiene il prezzo della variante da un item del carrello
     */
    public function get_cart_item_variant_price($cart_item) {
        $variant_data = $this->get_cart_item_variant_data($cart_item);
        
        if ($variant_data) {
            return floatval($variant_data['variant_price']);
        }
        
        return null;
    }
    
    /**
     * Rimuove tutti gli item del carrello che contengono una specifica variante
     */
    public function remove_variant_from_cart($variant_id) {
        $cart = WC()->cart;
        
        foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
            if (isset($cart_item['cpv_variant']) && $cart_item['cpv_variant']['variant_id'] == $variant_id) {
                $cart->remove_cart_item($cart_item_key);
            }
        }
    }
    
    /**
     * Conta quanti item del carrello utilizzano una specifica variante
     */
    public function count_variant_in_cart($variant_id) {
        $cart = WC()->cart;
        $count = 0;
        
        foreach ($cart->get_cart() as $cart_item) {
            if (isset($cart_item['cpv_variant']) && $cart_item['cpv_variant']['variant_id'] == $variant_id) {
                $count += $cart_item['quantity'];
            }
        }
        
        return $count;
    }
}
