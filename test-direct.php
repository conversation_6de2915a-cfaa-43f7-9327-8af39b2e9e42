<?php
/**
 * Test diretto del database senza AJAX
 */

// Carica WordPress
require_once('../../../wp-load.php');

// Carica le classi del plugin
require_once('includes/class-database.php');

$database = new CPV_Database();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Diretto Database</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>Test Diretto Database</h1>
    
    <h2>1. Verifica Tabella</h2>
    <?php
    $table_exists = $database->table_exists();
    if ($table_exists) {
        echo '<p class="success">✓ Tabella esiste</p>';
    } else {
        echo '<p class="error">✗ Tabella non esiste - Creazione...</p>';
        $database->create_tables();
        $table_exists = $database->table_exists();
        echo $table_exists ? '<p class="success">✓ Tabella creata</p>' : '<p class="error">✗ Errore creazione</p>';
    }
    ?>
    
    <h2>2. Test Inserimento</h2>
    <?php
    if ($table_exists) {
        $test_name = 'Variante Test ' . date('H:i:s');
        $test_price = 19.99;
        
        echo '<p>Inserimento variante: ' . $test_name . ' - €' . $test_price . '</p>';
        
        $variant_id = $database->add_variant(1, $test_name, '', $test_price);
        
        if (is_wp_error($variant_id)) {
            echo '<p class="error">✗ Errore: ' . $variant_id->get_error_message() . '</p>';
        } elseif ($variant_id) {
            echo '<p class="success">✓ Variante inserita con ID: ' . $variant_id . '</p>';
            
            // Recupera la variante
            $variant = $database->get_variant($variant_id);
            if ($variant) {
                echo '<h3>Dati variante:</h3>';
                echo '<pre>' . print_r($variant, true) . '</pre>';
            }
        } else {
            echo '<p class="error">✗ Inserimento fallito</p>';
        }
    }
    ?>
    
    <h2>3. Lista Varianti</h2>
    <?php
    if ($table_exists) {
        $variants = $database->get_product_variants(1, false); // false = include anche quelle inattive
        if (empty($variants)) {
            echo '<p class="info">Nessuna variante trovata per il prodotto 1</p>';
        } else {
            echo '<p class="info">Varianti trovate: ' . count($variants) . '</p>';
            echo '<pre>' . print_r($variants, true) . '</pre>';
        }
    }
    ?>
    
    <h2>4. Test Form Simulato</h2>
    <form method="post">
        <p>
            <label>Nome Variante:</label><br>
            <input type="text" name="test_name" value="Variante Form Test" required>
        </p>
        <p>
            <label>Prezzo:</label><br>
            <input type="number" name="test_price" step="0.01" value="29.99" required>
        </p>
        <p>
            <input type="submit" name="test_submit" value="Aggiungi Variante">
        </p>
    </form>
    
    <?php
    if (isset($_POST['test_submit'])) {
        echo '<h3>Risultato Form:</h3>';
        
        $name = sanitize_text_field($_POST['test_name']);
        $price = floatval($_POST['test_price']);
        
        if ($table_exists && !empty($name) && $price > 0) {
            $variant_id = $database->add_variant(1, $name, '', $price);
            
            if (is_wp_error($variant_id)) {
                echo '<p class="error">✗ Errore form: ' . $variant_id->get_error_message() . '</p>';
            } elseif ($variant_id) {
                echo '<p class="success">✓ Variante form inserita con ID: ' . $variant_id . '</p>';
            } else {
                echo '<p class="error">✗ Inserimento form fallito</p>';
            }
        } else {
            echo '<p class="error">✗ Dati form non validi</p>';
        }
    }
    ?>
    
    <h2>5. Informazioni Sistema</h2>
    <p><strong>WordPress Version:</strong> <?php echo get_bloginfo('version'); ?></p>
    <p><strong>WooCommerce:</strong> <?php echo class_exists('WooCommerce') ? 'Attivo' : 'Non attivo'; ?></p>
    <p><strong>Database Prefix:</strong> <?php global $wpdb; echo $wpdb->prefix; ?></p>
    <p><strong>Table Name:</strong> <?php echo $database->get_table_name(); ?></p>
    
</body>
</html>
