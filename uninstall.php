<?php
/**
 * File di disinstallazione del plugin Custom Product Variants
 * 
 * Questo file viene eseguito quando il plugin viene disinstallato
 * e si occupa di rimuovere tutti i dati del plugin dal database
 */

// Impedisce l'accesso diretto al file
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Verifica che sia davvero una disinstallazione
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Rimuove tutte le tabelle e i dati del plugin
 */
function cpv_uninstall_plugin() {
    global $wpdb;
    
    // Nome della tabella delle varianti
    $table_name = $wpdb->prefix . 'cpv_product_variants';
    
    // Rimuove la tabella delle varianti
    $wpdb->query("DROP TABLE IF EXISTS {$table_name}");
    
    // Rimuove le opzioni del plugin
    delete_option('cpv_plugin_version');
    delete_option('cpv_db_version');
    
    // Rimuove i metadati dei prodotti relativi alle varianti
    $wpdb->query("DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE 'cpv_%'");
    
    // Rimuove eventuali transient del plugin
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_cpv_%'");
    $wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_cpv_%'");
    
    // Pulisce la cache
    wp_cache_flush();
}

// Esegue la disinstallazione
cpv_uninstall_plugin();
