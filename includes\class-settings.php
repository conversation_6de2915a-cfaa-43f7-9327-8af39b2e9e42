<?php
/**
 * Classe per la gestione delle impostazioni del plugin
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

class CPV_Settings {

    /**
     * Istanza singleton
     */
    private static $instance = null;

    /**
     * Flag per evitare inizializzazioni multiple
     */
    private static $initialized = false;

    /**
     * Nome dell'opzione nel database
     */
    const OPTION_NAME = 'cpv_settings';

    /**
     * Slug della pagina di impostazioni
     */
    const PAGE_SLUG = 'cpv-settings';

    /**
     * Impostazioni predefinite
     */
    private $default_settings = array(
        'layout_style' => 'vertical', // vertical o horizontal
        'card_border_radius' => '8',
        'card_spacing' => '20',
        'enable_hover_effects' => true,
        'show_variant_count' => true,
    );

    /**
     * Costruttore privato per singleton
     */
    private function __construct() {
        if (!self::$initialized) {
            $this->init_hooks();
            self::$initialized = true;
        }
    }

    /**
     * Ottiene l'istanza singleton
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Previene la clonazione
     */
    private function __clone() {}

    /**
     * Previene la deserializzazione
     */
    public function __wakeup() {}

    /**
     * Inizializza gli hook
     */
    private function init_hooks() {
        // Hook per aggiungere la pagina di impostazioni
        add_action('admin_menu', array($this, 'add_settings_page'));

        // Hook per registrare le impostazioni
        add_action('admin_init', array($this, 'register_settings'));

        // Hook per caricare gli script admin
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Hook AJAX per preview del layout
        add_action('wp_ajax_cpv_preview_layout', array($this, 'ajax_preview_layout'));

        // Hook AJAX per reset impostazioni
        add_action('wp_ajax_cpv_reset_settings', array($this, 'ajax_reset_settings'));

        // Hook AJAX per pulire cache
        add_action('wp_ajax_cpv_clear_cache', array($this, 'ajax_clear_cache'));

        // Hook AJAX per ottenere contatori
        add_action('wp_ajax_cpv_get_counters', array($this, 'ajax_get_counters'));
    }

    /**
     * Aggiunge la pagina di impostazioni al menu admin
     */
    public function add_settings_page() {
        // Verifica se la pagina è già stata registrata
        global $submenu;
        if (isset($submenu['edit.php?post_type=product'])) {
            foreach ($submenu['edit.php?post_type=product'] as $item) {
                if (isset($item[2]) && $item[2] === self::PAGE_SLUG) {
                    return; // Pagina già registrata
                }
            }
        }

        add_submenu_page(
            'edit.php?post_type=product',
            __('Impostazioni Varianti Prodotto', 'custom-product-variants'),
            __('Varianti - Impostazioni', 'custom-product-variants'),
            'manage_woocommerce',
            self::PAGE_SLUG,
            array($this, 'render_settings_page')
        );
    }

    /**
     * Registra le impostazioni
     */
    public function register_settings() {
        // Verifica se le impostazioni sono già state registrate
        global $wp_settings_fields;
        if (isset($wp_settings_fields[self::PAGE_SLUG])) {
            return; // Impostazioni già registrate
        }

        register_setting(
            'cpv_settings_group',
            self::OPTION_NAME,
            array($this, 'sanitize_settings')
        );

        // Sezione Layout
        add_settings_section(
            'cpv_layout_section',
            __('Impostazioni Layout', 'custom-product-variants'),
            array($this, 'layout_section_callback'),
            self::PAGE_SLUG
        );

        // Campo Layout Style
        add_settings_field(
            'layout_style',
            __('Stile Layout Varianti', 'custom-product-variants'),
            array($this, 'layout_style_callback'),
            self::PAGE_SLUG,
            'cpv_layout_section'
        );

        // Campo Card Spacing
        add_settings_field(
            'card_spacing',
            __('Spaziatura tra Card (px)', 'custom-product-variants'),
            array($this, 'card_spacing_callback'),
            self::PAGE_SLUG,
            'cpv_layout_section'
        );

        // Campo Border Radius
        add_settings_field(
            'card_border_radius',
            __('Raggio Bordi Card (px)', 'custom-product-variants'),
            array($this, 'card_border_radius_callback'),
            self::PAGE_SLUG,
            'cpv_layout_section'
        );

        // Sezione Effetti
        add_settings_section(
            'cpv_effects_section',
            __('Effetti e Animazioni', 'custom-product-variants'),
            array($this, 'effects_section_callback'),
            self::PAGE_SLUG
        );

        // Campo Hover Effects
        add_settings_field(
            'enable_hover_effects',
            __('Abilita Effetti Hover', 'custom-product-variants'),
            array($this, 'enable_hover_effects_callback'),
            self::PAGE_SLUG,
            'cpv_effects_section'
        );

        // Campo Show Variant Count
        add_settings_field(
            'show_variant_count',
            __('Mostra Numero Varianti', 'custom-product-variants'),
            array($this, 'show_variant_count_callback'),
            self::PAGE_SLUG,
            'cpv_effects_section'
        );
    }

    /**
     * Callback per la sezione layout
     */
    public function layout_section_callback() {
        echo '<p>' . __('Configura l\'aspetto e il layout delle varianti prodotto.', 'custom-product-variants') . '</p>';
    }

    /**
     * Callback per la sezione effetti
     */
    public function effects_section_callback() {
        echo '<p>' . __('Configura gli effetti visivi e le animazioni.', 'custom-product-variants') . '</p>';
    }

    /**
     * Callback per il campo layout style
     */
    public function layout_style_callback() {
        $settings = $this->get_settings();
        $current_value = $settings['layout_style'];
        ?>
        <div class="cpv-layout-options">
            <div class="cpv-layout-option">
                <label>
                    <input type="radio" name="<?php echo self::OPTION_NAME; ?>[layout_style]" value="vertical" <?php checked($current_value, 'vertical'); ?>>
                    <div class="cpv-layout-preview cpv-layout-vertical">
                        <div class="cpv-preview-image"></div>
                        <div class="cpv-preview-content">
                            <div class="cpv-preview-title">Nome Variante</div>
                            <div class="cpv-preview-price">€29.99</div>
                        </div>
                    </div>
                    <span class="cpv-layout-label"><?php _e('Layout Verticale', 'custom-product-variants'); ?></span>
                    <small><?php _e('Immagine sopra, titolo e prezzo sotto', 'custom-product-variants'); ?></small>
                </label>
            </div>

            <div class="cpv-layout-option">
                <label>
                    <input type="radio" name="<?php echo self::OPTION_NAME; ?>[layout_style]" value="horizontal" <?php checked($current_value, 'horizontal'); ?>>
                    <div class="cpv-layout-preview cpv-layout-horizontal">
                        <div class="cpv-preview-image"></div>
                        <div class="cpv-preview-content">
                            <div class="cpv-preview-title">Nome Variante</div>
                            <div class="cpv-preview-price">€29.99</div>
                        </div>
                    </div>
                    <span class="cpv-layout-label"><?php _e('Layout Orizzontale', 'custom-product-variants'); ?></span>
                    <small><?php _e('Immagine a sinistra, titolo e prezzo a destra', 'custom-product-variants'); ?></small>
                </label>
            </div>
        </div>
        <p class="description">
            <?php _e('Scegli come visualizzare le varianti nella pagina prodotto.', 'custom-product-variants'); ?>
        </p>
        <?php
    }

    /**
     * Callback per il campo card spacing
     */
    public function card_spacing_callback() {
        $settings = $this->get_settings();
        $value = $settings['card_spacing'];
        ?>
        <input type="number" name="<?php echo self::OPTION_NAME; ?>[card_spacing]" value="<?php echo esc_attr($value); ?>" min="0" max="50" step="1" class="small-text">
        <span>px</span>
        <p class="description"><?php _e('Spazio tra le card delle varianti (0-50px).', 'custom-product-variants'); ?></p>
        <?php
    }

    /**
     * Callback per il campo border radius
     */
    public function card_border_radius_callback() {
        $settings = $this->get_settings();
        $value = $settings['card_border_radius'];
        ?>
        <input type="number" name="<?php echo self::OPTION_NAME; ?>[card_border_radius]" value="<?php echo esc_attr($value); ?>" min="0" max="25" step="1" class="small-text">
        <span>px</span>
        <p class="description"><?php _e('Raggio dei bordi delle card (0-25px).', 'custom-product-variants'); ?></p>
        <?php
    }

    /**
     * Callback per il campo hover effects
     */
    public function enable_hover_effects_callback() {
        $settings = $this->get_settings();
        $value = $settings['enable_hover_effects'];
        ?>
        <label>
            <input type="checkbox" name="<?php echo self::OPTION_NAME; ?>[enable_hover_effects]" value="1" <?php checked($value, true); ?>>
            <?php _e('Abilita effetti di hover sulle card delle varianti', 'custom-product-variants'); ?>
        </label>
        <p class="description"><?php _e('Animazioni e effetti quando si passa il mouse sulle varianti.', 'custom-product-variants'); ?></p>
        <?php
    }

    /**
     * Callback per il campo show variant count
     */
    public function show_variant_count_callback() {
        $settings = $this->get_settings();
        $value = $settings['show_variant_count'];
        ?>
        <label>
            <input type="checkbox" name="<?php echo self::OPTION_NAME; ?>[show_variant_count]" value="1" <?php checked($value, true); ?>>
            <?php _e('Mostra il numero di varianti disponibili nel titolo', 'custom-product-variants'); ?>
        </label>
        <p class="description"><?php _e('Aggiunge il conteggio delle varianti nel titolo della sezione.', 'custom-product-variants'); ?></p>
        <?php
    }

    /**
     * Renderizza la pagina di impostazioni
     */
    public function render_settings_page() {
        ?>
        <div class="wrap">
            <h1><?php _e('Impostazioni Varianti Prodotto', 'custom-product-variants'); ?></h1>

            <div class="cpv-settings-container">
                <div class="cpv-settings-main">
                    <form method="post" action="options.php">
                        <?php
                        settings_fields('cpv_settings_group');
                        do_settings_sections(self::PAGE_SLUG);
                        submit_button(__('Salva Impostazioni', 'custom-product-variants'));
                        ?>
                    </form>
                </div>

                <div class="cpv-settings-sidebar">
                    <div class="cpv-settings-box">
                        <h3><?php _e('Anteprima Layout', 'custom-product-variants'); ?></h3>
                        <div id="cpv-layout-preview-container">
                            <p><?php _e('Seleziona un layout per vedere l\'anteprima.', 'custom-product-variants'); ?></p>
                        </div>
                    </div>

                    <div class="cpv-settings-box">
                        <h3><?php _e('Informazioni Plugin', 'custom-product-variants'); ?></h3>
                        <p><strong><?php _e('Versione:', 'custom-product-variants'); ?></strong> <?php echo CPV_PLUGIN_VERSION; ?></p>
                        <p><strong><?php _e('Varianti Totali:', 'custom-product-variants'); ?></strong> <span id="cpv-total-variants"><?php echo $this->get_total_variants_count(); ?></span></p>
                        <p><strong><?php _e('Prodotti con Varianti:', 'custom-product-variants'); ?></strong> <span id="cpv-products-with-variants"><?php echo $this->get_products_with_variants_count(); ?></span></p>
                    </div>

                    <div class="cpv-settings-box">
                        <h3><?php _e('Azioni Rapide', 'custom-product-variants'); ?></h3>
                        <p>
                            <button type="button" class="button" id="cpv-reset-settings">
                                <?php _e('Ripristina Impostazioni', 'custom-product-variants'); ?>
                            </button>
                        </p>
                        <p>
                            <button type="button" class="button" id="cpv-clear-cache">
                                <?php _e('Pulisci Cache', 'custom-product-variants'); ?>
                            </button>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Sanitizza le impostazioni prima del salvataggio
     */
    public function sanitize_settings($input) {
        $sanitized = array();

        // Layout style
        $sanitized['layout_style'] = in_array($input['layout_style'], array('vertical', 'horizontal')) ? $input['layout_style'] : 'vertical';

        // Card spacing
        $sanitized['card_spacing'] = absint($input['card_spacing']);
        if ($sanitized['card_spacing'] > 50) $sanitized['card_spacing'] = 50;

        // Border radius
        $sanitized['card_border_radius'] = absint($input['card_border_radius']);
        if ($sanitized['card_border_radius'] > 25) $sanitized['card_border_radius'] = 25;

        // Boolean values
        $sanitized['enable_hover_effects'] = isset($input['enable_hover_effects']) ? true : false;
        $sanitized['show_variant_count'] = isset($input['show_variant_count']) ? true : false;

        return $sanitized;
    }

    /**
     * Ottiene le impostazioni correnti
     */
    public function get_settings() {
        $settings = get_option(self::OPTION_NAME, array());
        return wp_parse_args($settings, $this->default_settings);
    }

    /**
     * Ottiene una singola impostazione
     */
    public function get_setting($key, $default = null) {
        $settings = $this->get_settings();
        return isset($settings[$key]) ? $settings[$key] : $default;
    }

    /**
     * Aggiorna una singola impostazione
     */
    public function update_setting($key, $value) {
        $settings = $this->get_settings();
        $settings[$key] = $value;
        return update_option(self::OPTION_NAME, $settings);
    }

    /**
     * Resetta le impostazioni ai valori predefiniti
     */
    public function reset_settings() {
        return update_option(self::OPTION_NAME, $this->default_settings);
    }

    /**
     * Ottiene il numero totale di varianti
     */
    public function get_total_variants_count() {
        global $wpdb;
        $database = new CPV_Database();
        $table_name = $database->get_table_name();

        $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table_name}");
        return intval($count);
    }

    /**
     * Ottiene il numero di prodotti con varianti
     */
    public function get_products_with_variants_count() {
        global $wpdb;
        $database = new CPV_Database();
        $table_name = $database->get_table_name();

        $count = $wpdb->get_var("SELECT COUNT(DISTINCT product_id) FROM {$table_name}");
        return intval($count);
    }

    /**
     * Carica gli script per la pagina di impostazioni
     */
    public function enqueue_admin_scripts($hook) {
        // Carica solo nella pagina di impostazioni
        if (strpos($hook, self::PAGE_SLUG) === false) {
            return;
        }

        wp_enqueue_style(
            'cpv-settings-css',
            CPV_PLUGIN_URL . 'assets/css/settings.css',
            array(),
            CPV_PLUGIN_VERSION
        );

        wp_enqueue_script(
            'cpv-settings-js',
            CPV_PLUGIN_URL . 'assets/js/settings.js',
            array('jquery'),
            CPV_PLUGIN_VERSION,
            true
        );

        wp_localize_script('cpv-settings-js', 'cpv_settings', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cpv_settings_nonce'),
            'strings' => array(
                'preview_loading' => __('Caricamento anteprima...', 'custom-product-variants'),
                'settings_saved' => __('Impostazioni salvate con successo!', 'custom-product-variants'),
                'settings_error' => __('Errore nel salvataggio delle impostazioni.', 'custom-product-variants'),
                'reset_confirm' => __('Sei sicuro di voler ripristinare le impostazioni predefinite?', 'custom-product-variants'),
                'cache_cleared' => __('Cache pulita con successo!', 'custom-product-variants'),
            )
        ));
    }

    /**
     * AJAX: Preview del layout
     */
    public function ajax_preview_layout() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_settings_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        $layout_style = sanitize_text_field($_POST['layout_style']);

        if (!in_array($layout_style, array('vertical', 'horizontal'))) {
            wp_send_json_error(__('Layout non valido.', 'custom-product-variants'));
        }

        // Genera HTML di preview
        ob_start();
        $this->render_layout_preview($layout_style);
        $html = ob_get_clean();

        wp_send_json_success(array(
            'html' => $html,
            'layout' => $layout_style
        ));
    }

    /**
     * Renderizza il preview del layout
     */
    private function render_layout_preview($layout_style) {
        $class = 'cpv-preview-container cpv-layout-' . $layout_style;
        ?>
        <div class="<?php echo esc_attr($class); ?>">
            <div class="cpv-preview-card">
                <div class="cpv-preview-image">
                    <div class="cpv-preview-placeholder">📷</div>
                </div>
                <div class="cpv-preview-content">
                    <h4 class="cpv-preview-title">Variante Esempio</h4>
                    <div class="cpv-preview-price">€29.99</div>
                    <button class="cpv-preview-button">Seleziona</button>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * AJAX: Reset impostazioni
     */
    public function ajax_reset_settings() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_settings_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Non hai i permessi per eseguire questa azione.', 'custom-product-variants'));
        }

        // Reset delle impostazioni
        $result = $this->reset_settings();

        if ($result) {
            wp_send_json_success(__('Impostazioni ripristinate con successo.', 'custom-product-variants'));
        } else {
            wp_send_json_error(__('Errore nel ripristino delle impostazioni.', 'custom-product-variants'));
        }
    }

    /**
     * AJAX: Pulisci cache
     */
    public function ajax_clear_cache() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_settings_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error(__('Non hai i permessi per eseguire questa azione.', 'custom-product-variants'));
        }

        // Pulisce varie cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }

        // Pulisce cache di WooCommerce se disponibile
        if (function_exists('wc_delete_product_transients')) {
            wc_delete_product_transients();
        }

        // Pulisce cache di oggetti
        wp_cache_delete_group('cpv_variants');

        wp_send_json_success(__('Cache pulita con successo.', 'custom-product-variants'));
    }

    /**
     * AJAX: Ottieni contatori
     */
    public function ajax_get_counters() {
        // Verifica nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_settings_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        $data = array(
            'total_variants' => $this->get_total_variants_count(),
            'products_with_variants' => $this->get_products_with_variants_count()
        );

        wp_send_json_success($data);
    }
}