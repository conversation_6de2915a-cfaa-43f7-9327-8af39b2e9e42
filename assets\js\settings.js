/**
 * JavaScript per la pagina di impostazioni del plugin Custom Product Variants
 */

jQuery(document).ready(function($) {
    'use strict';

    // Inizializza la pagina di impostazioni
    initSettingsPage();

    /**
     * Inizializza la pagina di impostazioni
     */
    function initSettingsPage() {
        // Gestisce il cambio di layout
        handleLayoutChange();

        // Gestisce i pulsanti di azione rapida
        handleQuickActions();

        // Mostra l'anteprima del layout corrente
        showCurrentLayoutPreview();

        // Gestisce il salvataggio del form
        handleFormSubmit();
    }

    /**
     * Gestisce il cambio di layout
     */
    function handleLayoutChange() {
        $('input[name="cpv_settings[layout_style]"]').on('change', function() {
            var selectedLayout = $(this).val();

            // Aggiorna le classi visive
            $('.cpv-layout-option').removeClass('selected');
            $(this).closest('.cpv-layout-option').addClass('selected');

            // Mostra l'anteprima del layout selezionato
            showLayoutPreview(selectedLayout);
        });
    }

    /**
     * Mostra l'anteprima del layout corrente
     */
    function showCurrentLayoutPreview() {
        var currentLayout = $('input[name="cpv_settings[layout_style]"]:checked').val();
        if (currentLayout) {
            $('.cpv-layout-option').removeClass('selected');
            $('input[name="cpv_settings[layout_style]"]:checked').closest('.cpv-layout-option').addClass('selected');
            showLayoutPreview(currentLayout);
        }
    }

    /**
     * Mostra l'anteprima del layout
     */
    function showLayoutPreview(layoutStyle) {
        var $container = $('#cpv-layout-preview-container');

        // Mostra loading
        $container.html('<p>' + cpv_settings.strings.preview_loading + '</p>');

        // Crea l'anteprima direttamente (senza AJAX per semplicità)
        var previewHtml = generatePreviewHtml(layoutStyle);
        $container.html(previewHtml);
    }

    /**
     * Genera l'HTML dell'anteprima
     */
    function generatePreviewHtml(layoutStyle) {
        var containerClass = 'cpv-preview-container cpv-layout-' + layoutStyle;

        if (layoutStyle === 'two-columns') {
            return '<div class="' + containerClass + '">' +
                       '<div class="cpv-preview-grid">' +
                           '<div class="cpv-preview-card">' +
                               '<div class="cpv-preview-image">' +
                                   '<div class="cpv-preview-placeholder">📷</div>' +
                               '</div>' +
                               '<div class="cpv-preview-content">' +
                                   '<h4 class="cpv-preview-title">Variante 1</h4>' +
                                   '<div class="cpv-preview-price">€29.99</div>' +
                                   '<button class="cpv-preview-button">Seleziona</button>' +
                               '</div>' +
                           '</div>' +
                           '<div class="cpv-preview-card">' +
                               '<div class="cpv-preview-image">' +
                                   '<div class="cpv-preview-placeholder">📷</div>' +
                               '</div>' +
                               '<div class="cpv-preview-content">' +
                                   '<h4 class="cpv-preview-title">Variante 2</h4>' +
                                   '<div class="cpv-preview-price">€34.99</div>' +
                                   '<button class="cpv-preview-button">Seleziona</button>' +
                               '</div>' +
                           '</div>' +
                       '</div>' +
                   '</div>';
        } else {
            return '<div class="' + containerClass + '">' +
                       '<div class="cpv-preview-card">' +
                           '<div class="cpv-preview-image">' +
                               '<div class="cpv-preview-placeholder">📷</div>' +
                           '</div>' +
                           '<div class="cpv-preview-content">' +
                               '<h4 class="cpv-preview-title">Variante Esempio</h4>' +
                               '<div class="cpv-preview-price">€29.99</div>' +
                               '<button class="cpv-preview-button">Seleziona</button>' +
                           '</div>' +
                       '</div>' +
                   '</div>';
        }
    }

    /**
     * Gestisce i pulsanti di azione rapida
     */
    function handleQuickActions() {
        // Pulsante reset impostazioni
        $('#cpv-reset-settings').on('click', function(e) {
            e.preventDefault();

            if (confirm(cpv_settings.strings.reset_confirm)) {
                resetSettings();
            }
        });

        // Pulsante pulisci cache
        $('#cpv-clear-cache').on('click', function(e) {
            e.preventDefault();
            clearCache();
        });
    }

    /**
     * Resetta le impostazioni
     */
    function resetSettings() {
        var $button = $('#cpv-reset-settings');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Ripristino...');

        $.ajax({
            url: cpv_settings.ajax_url,
            type: 'POST',
            data: {
                action: 'cpv_reset_settings',
                nonce: cpv_settings.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(cpv_settings.strings.settings_saved, 'success');
                    // Ricarica la pagina per mostrare le impostazioni resettate
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showNotice(response.data || cpv_settings.strings.settings_error, 'error');
                }
            },
            error: function() {
                showNotice(cpv_settings.strings.settings_error, 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Pulisce la cache
     */
    function clearCache() {
        var $button = $('#cpv-clear-cache');
        var originalText = $button.text();

        $button.prop('disabled', true).text('Pulizia...');

        $.ajax({
            url: cpv_settings.ajax_url,
            type: 'POST',
            data: {
                action: 'cpv_clear_cache',
                nonce: cpv_settings.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(cpv_settings.strings.cache_cleared, 'success');
                } else {
                    showNotice(response.data || 'Errore nella pulizia della cache.', 'error');
                }
            },
            error: function() {
                showNotice('Errore nella pulizia della cache.', 'error');
            },
            complete: function() {
                $button.prop('disabled', false).text(originalText);
            }
        });
    }

    /**
     * Gestisce il salvataggio del form
     */
    function handleFormSubmit() {
        $('form').on('submit', function() {
            var $submitButton = $(this).find('input[type="submit"]');
            var originalValue = $submitButton.val();

            $submitButton.prop('disabled', true).val('Salvataggio...');

            // Ripristina il pulsante dopo un po' (WordPress gestisce il salvataggio)
            setTimeout(function() {
                $submitButton.prop('disabled', false).val(originalValue);
            }, 2000);
        });
    }

    /**
     * Mostra un messaggio di notifica
     */
    function showNotice(message, type) {
        type = type || 'info';

        var noticeClass = 'notice notice-' + type + ' is-dismissible';
        var $notice = $('<div class="' + noticeClass + '"><p>' + message + '</p></div>');

        // Rimuove eventuali notice precedenti
        $('.wrap .notice').remove();

        // Aggiunge la nuova notice
        $('.wrap h1').after($notice);

        // Auto-rimuove dopo 5 secondi
        setTimeout(function() {
            $notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    /**
     * Aggiorna i contatori nella sidebar
     */
    function updateCounters() {
        $.ajax({
            url: cpv_settings.ajax_url,
            type: 'POST',
            data: {
                action: 'cpv_get_counters',
                nonce: cpv_settings.nonce
            },
            success: function(response) {
                if (response.success) {
                    $('#cpv-total-variants').text(response.data.total_variants);
                    $('#cpv-products-with-variants').text(response.data.products_with_variants);
                }
            }
        });
    }

    // Aggiorna i contatori ogni 30 secondi
    setInterval(updateCounters, 30000);
});