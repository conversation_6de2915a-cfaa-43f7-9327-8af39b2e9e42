/**
 * Stili CSS per la pagina di impostazioni del plugin Custom Product Variants
 */

/* Container principale */
.cpv-settings-container {
    display: flex;
    gap: 30px;
    margin-top: 20px;
}

.cpv-settings-main {
    flex: 2;
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.cpv-settings-sidebar {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.cpv-settings-box {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0,0,0,0.04);
}

.cpv-settings-box h3 {
    margin: 0 0 15px 0;
    padding: 0 0 10px 0;
    border-bottom: 1px solid #eee;
    font-size: 16px;
    font-weight: 600;
}

/* Layout Options */
.cpv-layout-options {
    display: flex;
    gap: 20px;
    margin: 15px 0;
}

.cpv-layout-option {
    flex: 1;
    border: 2px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.cpv-layout-option:hover {
    border-color: #0073aa;
    box-shadow: 0 2px 8px rgba(0,115,170,0.1);
}

.cpv-layout-option input[type="radio"] {
    position: absolute;
    top: 10px;
    right: 10px;
    margin: 0;
}

.cpv-layout-option input[type="radio"]:checked + .cpv-layout-preview {
    border-color: #0073aa;
}

.cpv-layout-option.selected {
    border-color: #0073aa;
    background: #f0f8ff;
}

/* Layout Preview */
.cpv-layout-preview {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    min-height: 80px;
    display: flex;
    align-items: center;
    background: #fafafa;
}

.cpv-layout-vertical {
    flex-direction: column;
    text-align: center;
}

.cpv-layout-horizontal {
    flex-direction: row;
    text-align: left;
}

.cpv-layout-two-columns {
    flex-direction: column;
    text-align: center;
}

.cpv-preview-grid {
    display: flex;
    gap: 10px;
    width: 100%;
}

.cpv-preview-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background: #fff;
}

.cpv-preview-image {
    width: 40px;
    height: 40px;
    background: #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #666;
    flex-shrink: 0;
}

.cpv-layout-vertical .cpv-preview-image {
    margin-bottom: 8px;
}

.cpv-layout-horizontal .cpv-preview-image {
    margin-right: 10px;
}

.cpv-preview-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.cpv-layout-horizontal .cpv-preview-content {
    flex: 1;
}

.cpv-preview-title {
    font-size: 12px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.cpv-preview-price {
    font-size: 11px;
    font-weight: 700;
    color: #0073aa;
    margin: 0;
}

.cpv-layout-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.cpv-layout-option small {
    color: #666;
    font-size: 12px;
}

/* Preview Container nella sidebar */
#cpv-layout-preview-container {
    min-height: 120px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    background: #fafafa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cpv-preview-container {
    width: 100%;
}

.cpv-preview-card {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    background: #fff;
    display: flex;
    align-items: center;
}

.cpv-preview-container.cpv-layout-vertical .cpv-preview-card {
    flex-direction: column;
    text-align: center;
    max-width: 150px;
    margin: 0 auto;
}

.cpv-preview-container.cpv-layout-horizontal .cpv-preview-card {
    flex-direction: row;
}

.cpv-preview-container .cpv-preview-image {
    width: 50px;
    height: 50px;
    background: #e0e0e0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.cpv-preview-container.cpv-layout-vertical .cpv-preview-image {
    margin-bottom: 10px;
}

.cpv-preview-container.cpv-layout-horizontal .cpv-preview-image {
    margin-right: 12px;
}

.cpv-preview-container .cpv-preview-title {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #333;
}

.cpv-preview-container .cpv-preview-price {
    font-size: 13px;
    font-weight: 700;
    color: #0073aa;
    margin: 0 0 8px 0;
}

.cpv-preview-button {
    background: #0073aa;
    color: #fff;
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
}

/* Responsive */
@media (max-width: 1200px) {
    .cpv-settings-container {
        flex-direction: column;
    }

    .cpv-layout-options {
        flex-direction: column;
    }

    .cpv-layout-option {
        max-width: 300px;
    }
}