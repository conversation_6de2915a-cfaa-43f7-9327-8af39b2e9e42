<?php
/**
 * Test per verificare il posizionamento delle varianti e i prezzi nel carrello
 */

// Impedisce l'accesso diretto
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__FILE__) . '/');
}

?>
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Posizionamento e Carrello - Custom Product Variants</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 10px;
        }
        .fix-item {
            background: #f0f8ff;
            border-left: 4px solid #0073aa;
            padding: 15px;
            margin: 10px 0;
        }
        .fix-item h3 {
            margin-top: 0;
            color: #0073aa;
        }
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        .priority-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .priority-table th,
        .priority-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .priority-table th {
            background: #f9f9f9;
            font-weight: bold;
        }
        .highlight {
            background: #fff3cd;
            font-weight: bold;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Test Posizionamento e Carrello - Custom Product Variants</h1>
        <p>Questo documento mostra le modifiche apportate per risolvere i problemi di posizionamento e prezzo nel mini-cart.</p>

        <!-- Problema 1: Posizionamento -->
        <div class="test-section">
            <h2>🎯 Problema 1: Posizionamento delle Varianti</h2>
            
            <div class="fix-item">
                <h3>✅ Soluzione Implementata</h3>
                <p>Modificata la priorità dell'hook da <code>30</code> a <code>35</code> per assicurarsi che le varianti appaiano dopo gli attributi WooCommerce.</p>
                
                <div class="code-block">
// includes/class-frontend.php - Riga 37
add_action('woocommerce_single_product_summary', array($this, 'display_product_variants'), 35);
                </div>
            </div>

            <h3>📋 Ordine di Visualizzazione WooCommerce</h3>
            <table class="priority-table">
                <thead>
                    <tr>
                        <th>Priorità</th>
                        <th>Elemento</th>
                        <th>Hook</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>5</td>
                        <td>Titolo Prodotto</td>
                        <td>woocommerce_single_product_summary</td>
                    </tr>
                    <tr>
                        <td>10</td>
                        <td>Prezzo</td>
                        <td>woocommerce_single_product_summary</td>
                    </tr>
                    <tr>
                        <td>20</td>
                        <td>Descrizione Breve</td>
                        <td>woocommerce_single_product_summary</td>
                    </tr>
                    <tr>
                        <td>25</td>
                        <td>Rating</td>
                        <td>woocommerce_single_product_summary</td>
                    </tr>
                    <tr>
                        <td>30</td>
                        <td>Form Add-to-Cart + Attributi</td>
                        <td>woocommerce_single_product_summary</td>
                    </tr>
                    <tr class="highlight">
                        <td>35</td>
                        <td><strong>🆕 Varianti Personalizzate</strong></td>
                        <td>woocommerce_single_product_summary</td>
                    </tr>
                    <tr>
                        <td>40</td>
                        <td>Meta Prodotto</td>
                        <td>woocommerce_single_product_summary</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Problema 2: Prezzo Mini-Cart -->
        <div class="test-section">
            <h2>💰 Problema 2: Prezzo nel Mini-Cart</h2>
            
            <div class="fix-item">
                <h3>✅ Soluzioni Implementate</h3>
                
                <h4>1. Hook Specifici per Mini-Cart</h4>
                <div class="code-block">
// includes/class-cart-integration.php - Righe 42-47
add_filter('woocommerce_widget_cart_item_quantity', array($this, 'widget_cart_item_quantity'), 10, 3);
add_filter('woocommerce_cart_item_price', array($this, 'cart_item_price'), 10, 3);
add_action('woocommerce_add_to_cart', array($this, 'refresh_cart_fragments'), 10, 6);
                </div>

                <h4>2. Funzione per Prezzo Carrello</h4>
                <div class="code-block">
public function cart_item_price($price, $cart_item, $cart_item_key) {
    if (isset($cart_item['cpv_variant'])) {
        $variant_data = $cart_item['cpv_variant'];
        $price = wc_price($variant_data['variant_price']);
    }
    return $price;
}
                </div>

                <h4>3. Miglioramento before_calculate_totals</h4>
                <div class="code-block">
public function before_calculate_totals($cart) {
    // Evita loop infiniti
    if (did_action('woocommerce_before_calculate_totals') >= 2) {
        return;
    }
    
    foreach ($cart->get_cart() as $cart_item_key => $cart_item) {
        if (isset($cart_item['cpv_variant'])) {
            $variant_data = $cart_item['cpv_variant'];
            // Forza il prezzo della variante
            $cart_item['data']->set_price(floatval($variant_data['variant_price']));
        }
    }
}
                </div>
            </div>

            <h3>🔄 Flusso di Aggiornamento Prezzi</h3>
            <ol>
                <li><strong>Aggiunta al Carrello:</strong> Il prezzo della variante viene salvato nei dati del carrello</li>
                <li><strong>Calcolo Totali:</strong> <code>before_calculate_totals</code> forza il prezzo corretto</li>
                <li><strong>Visualizzazione Carrello:</strong> <code>cart_item_price</code> formatta il prezzo per la visualizzazione</li>
                <li><strong>Mini-Cart:</strong> <code>widget_cart_item_quantity</code> assicura la coerenza nel mini-cart</li>
                <li><strong>Aggiornamento AJAX:</strong> <code>refresh_cart_fragments</code> aggiorna i frammenti del carrello</li>
            </ol>
        </div>

        <!-- Test di Verifica -->
        <div class="test-section">
            <h2>🧪 Come Testare le Modifiche</h2>
            
            <h3>Test 1: Posizionamento</h3>
            <ol>
                <li>Vai su una pagina prodotto che ha sia attributi WooCommerce che varianti personalizzate</li>
                <li>Verifica che l'ordine sia:
                    <ul>
                        <li>Titolo e prezzo</li>
                        <li>Attributi WooCommerce (es. colore, taglia)</li>
                        <li><strong>Varianti personalizzate</strong> ← Dovrebbero apparire qui</li>
                    </ul>
                </li>
            </ol>

            <h3>Test 2: Prezzo Mini-Cart</h3>
            <ol>
                <li>Seleziona un attributo WooCommerce (es. colore verde = €5)</li>
                <li>Seleziona una variante personalizzata (es. Test Variante = €20)</li>
                <li>Aggiungi al carrello</li>
                <li>Controlla il mini-cart: dovrebbe mostrare <span class="success">€20.00</span> (prezzo variante)</li>
                <li>Controlla il carrello completo: dovrebbe mostrare <span class="success">€20.00</span> (prezzo variante)</li>
            </ol>

            <h3>Test 3: Coerenza Prezzi</h3>
            <ol>
                <li>Aggiungi più prodotti con varianti diverse</li>
                <li>Verifica che ogni prodotto mostri il prezzo corretto della sua variante</li>
                <li>Verifica che il totale sia calcolato correttamente</li>
            </ol>
        </div>

        <!-- Risoluzione Problemi -->
        <div class="test-section">
            <h2>🔧 Risoluzione Problemi</h2>
            
            <h3>Se il posizionamento non funziona ancora:</h3>
            <div class="code-block">
// Prova ad aumentare ulteriormente la priorità
add_action('woocommerce_single_product_summary', array($this, 'display_product_variants'), 40);

// Oppure usa un hook diverso
add_action('woocommerce_after_single_product_summary', array($this, 'display_product_variants'), 5);
            </div>

            <h3>Se il prezzo nel mini-cart non si aggiorna:</h3>
            <ol>
                <li>Svuota la cache del browser</li>
                <li>Verifica che non ci siano plugin di cache che interferiscono</li>
                <li>Controlla la console del browser per errori JavaScript</li>
                <li>Verifica che il tema supporti correttamente i frammenti del carrello AJAX</li>
            </ol>

            <h3>Debug Avanzato:</h3>
            <div class="code-block">
// Aggiungi questo nel file functions.php del tema per debug
add_action('wp_footer', function() {
    if (is_product()) {
        echo '&lt;script&gt;console.log("Hooks WooCommerce:", window.wc_single_product_params);&lt;/script&gt;';
    }
});
            </div>
        </div>

        <!-- Riepilogo -->
        <div class="test-section">
            <h2>📋 Riepilogo Modifiche</h2>
            
            <h3>File Modificati:</h3>
            <ul>
                <li><strong>includes/class-frontend.php</strong>
                    <ul>
                        <li>Riga 37: Priorità hook cambiata da 30 a 35</li>
                    </ul>
                </li>
                <li><strong>includes/class-cart-integration.php</strong>
                    <ul>
                        <li>Righe 42-47: Aggiunti hook per mini-cart</li>
                        <li>Righe 121-141: Migliorata funzione before_calculate_totals</li>
                        <li>Righe 170-181: Aggiunta funzione cart_item_price</li>
                        <li>Righe 182-206: Aggiunte funzioni per mini-cart e refresh</li>
                    </ul>
                </li>
            </ul>

            <h3>Risultati Attesi:</h3>
            <ul class="success">
                <li>✅ Varianti personalizzate appaiono dopo gli attributi WooCommerce</li>
                <li>✅ Prezzo nel mini-cart mostra il prezzo della variante, non dell'attributo</li>
                <li>✅ Prezzo nel carrello completo è corretto</li>
                <li>✅ Totali calcolati correttamente</li>
                <li>✅ Compatibilità mantenuta con tutte le funzionalità esistenti</li>
            </ul>
        </div>
    </div>
</body>
</html>
