/**
 * Stili CSS per il pannello amministrativo del plugin Custom Product Variants
 */

/* Container principale delle varianti */
.cpv-variants-container {
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.cpv-variants-header h3 {
    margin-top: 0;
    color: #23282d;
    font-size: 18px;
    font-weight: 600;
}

.cpv-variants-header .description {
    color: #666;
    font-style: italic;
    margin-bottom: 20px;
}

/* Sezione aggiunta variante */
.cpv-add-variant-section {
    background: #f9f9f9;
    padding: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    margin-bottom: 30px;
}

.cpv-add-variant-section h4 {
    margin-top: 0;
    color: #23282d;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

/* Nuovo layout form */
.cpv-form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.cpv-form-field {
    flex: 1;
    min-width: 250px;
}

.cpv-form-field label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #23282d;
}

.cpv-form-field input[type="text"],
.cpv-form-field input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.cpv-form-field input[type="text"]:focus,
.cpv-form-field input[type="number"]:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.cpv-form-field .description {
    margin-top: 5px;
    font-size: 13px;
    color: #666;
    font-style: italic;
}

.cpv-field-name {
    flex: 2;
}

.cpv-field-price {
    flex: 1;
    min-width: 200px;
}

.cpv-field-image {
    flex: 1;
    min-width: 300px;
}

.cpv-price-input-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
}

.cpv-price-input-wrapper input {
    flex: 1;
}

.cpv-price-input-wrapper .currency-symbol {
    font-weight: bold;
    color: #666;
    font-size: 16px;
}

.cpv-variant-form .required {
    color: #d63638;
}

.cpv-variant-form .currency-symbol {
    margin-left: 5px;
    font-weight: bold;
    color: #666;
}

/* Form Actions */
.cpv-form-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
    display: flex;
    align-items: center;
    gap: 15px;
}

.cpv-submit-btn {
    position: relative;
    min-width: 150px;
}

.cpv-submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.cpv-form-messages {
    flex: 1;
}

/* Upload immagine */
.cpv-image-upload {
    position: relative;
}

.cpv-image-preview {
    margin-bottom: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f9f9f9;
    text-align: center;
}

.cpv-image-preview img {
    display: block;
    margin: 0 auto 10px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cpv-image-actions {
    margin-top: 10px;
}

.cpv-remove-image {
    background: #d63638 !important;
    border-color: #d63638 !important;
    color: #fff !important;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
}

.cpv-remove-image:hover {
    background: #b32d2e !important;
    border-color: #b32d2e !important;
}

.cpv-upload-image {
    background: #0073aa !important;
    border-color: #0073aa !important;
    color: #fff !important;
}

.cpv-upload-image:hover {
    background: #005a87 !important;
    border-color: #005a87 !important;
}

/* Loading spinner */
.cpv-loading {
    display: inline-block;
    margin-left: 10px;
    color: #666;
    font-style: italic;
}

.cpv-loading:before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 5px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: cpv-spin 1s linear infinite;
    vertical-align: middle;
}

@keyframes cpv-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Sezione lista varianti */
.cpv-variants-list-section h4 {
    color: #23282d;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 20px;
}

.cpv-no-variants {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 40px 20px;
    background: #f9f9f9;
    border: 1px dashed #ddd;
    border-radius: 4px;
}

/* Tabella varianti */
.cpv-variants-table-container {
    overflow-x: auto;
}

.cpv-variants-table-container table {
    width: 100%;
    border-collapse: collapse;
}

.cpv-variants-table-container th,
.cpv-variants-table-container td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.cpv-variants-table-container th {
    background: #f1f1f1;
    font-weight: 600;
    color: #23282d;
}

.cpv-col-image {
    width: 80px;
    text-align: center;
}

.cpv-col-name {
    width: 30%;
}

.cpv-col-price {
    width: 15%;
}

.cpv-col-status {
    width: 15%;
}

.cpv-col-actions {
    width: 25%;
}

.cpv-no-image {
    color: #666;
    font-size: 12px;
    font-style: italic;
}

/* Stati varianti */
.cpv-status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.cpv-status-active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cpv-status-inactive {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Pulsanti azioni */
.cpv-col-actions .button {
    margin-right: 5px;
    margin-bottom: 5px;
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

.cpv-edit-variant {
    background: #0073aa !important;
    border-color: #0073aa !important;
    color: #fff !important;
}

.cpv-edit-variant:hover {
    background: #005a87 !important;
    border-color: #005a87 !important;
}

.cpv-toggle-variant {
    background: #f0ad4e !important;
    border-color: #f0ad4e !important;
    color: #fff !important;
}

.cpv-toggle-variant:hover {
    background: #ec971f !important;
    border-color: #ec971f !important;
}

.cpv-delete-variant {
    background: #d63638 !important;
    border-color: #d63638 !important;
    color: #fff !important;
}

.cpv-delete-variant:hover {
    background: #b32d2e !important;
    border-color: #b32d2e !important;
}

/* Sortable */
.cpv-sortable {
    cursor: move;
}

.cpv-sortable tr:hover {
    background: #f9f9f9;
}

.cpv-sortable .ui-sortable-helper {
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cpv-sortable .ui-sortable-placeholder {
    background: #f0f0f0;
    border: 2px dashed #ddd;
}

/* Messaggi del form */
.cpv-form-message {
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    margin-top: 10px;
}

.cpv-form-message-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cpv-form-message-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cpv-form-message-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Messaggi di notifica globali */
.cpv-notice {
    padding: 12px;
    margin: 15px 0;
    border-left: 4px solid;
    border-radius: 4px;
}

.cpv-notice-success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.cpv-notice-error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.cpv-notice-warning {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.cpv-notice-info {
    background: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

/* Responsive */
@media (max-width: 768px) {
    .cpv-variants-container {
        padding: 15px;
    }
    
    .cpv-add-variant-section {
        padding: 15px;
    }
    
    .cpv-variant-form .form-table th {
        width: auto;
        display: block;
        padding-bottom: 5px;
    }
    
    .cpv-variant-form .form-table td {
        display: block;
        padding-top: 5px;
    }
    
    .cpv-variants-table-container {
        font-size: 14px;
    }
    
    .cpv-col-actions .button {
        display: block;
        width: 100%;
        margin-bottom: 5px;
        text-align: center;
    }
}
