<?php
/**
 * Plugin Name: Varianti Prodotto Personalizzate
 * Plugin URI: https://example.com/custom-product-variants
 * Description: Plugin per WooCommerce che permette la creazione e gestione di varianti personalizzate di prodotti con visualizzazione e acquisto diretto dalla pagina prodotto.
 * Version: 1.0.0
 * Author: JoJoD3v
 * Author URI: https://example.com
 * Text Domain: custom-product-variants
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 4.0
 * WC tested up to: 8.0
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

// Definisce le costanti del plugin
define('CPV_PLUGIN_FILE', __FILE__);
define('CPV_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('CPV_PLUGIN_URL', plugin_dir_url(__FILE__));
define('CPV_PLUGIN_VERSION', '1.0.0');
define('CPV_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Classe principale del plugin Custom Product Variants
 */
class Custom_Product_Variants {
    
    /**
     * Istanza singleton della classe
     */
    private static $instance = null;
    
    /**
     * Istanza della classe Database
     */
    public $database;
    
    /**
     * Istanza della classe Admin
     */
    public $admin;
    
    /**
     * Istanza della classe Frontend
     */
    public $frontend;
    
    /**
     * Istanza della classe Cart Integration
     */
    public $cart_integration;

    /**
     * Istanza della classe Settings
     */
    public $settings;
    
    /**
     * Costruttore privato per implementare il pattern singleton
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Restituisce l'istanza singleton della classe
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Inizializza gli hook del plugin
     */
    private function init_hooks() {
        // Hook per l'attivazione del plugin
        register_activation_hook(CPV_PLUGIN_FILE, array($this, 'activate'));
        
        // Hook per la disattivazione del plugin
        register_deactivation_hook(CPV_PLUGIN_FILE, array($this, 'deactivate'));
        
        // Hook per l'inizializzazione del plugin
        add_action('plugins_loaded', array($this, 'init'));
        
        // Hook per il caricamento dei file di traduzione
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        
        // Hook per verificare se WooCommerce è attivo
        add_action('admin_init', array($this, 'check_woocommerce'));
    }
    
    /**
     * Inizializza il plugin
     */
    public function init() {
        // Verifica se WooCommerce è attivo
        if (!$this->is_woocommerce_active()) {
            return;
        }
        
        // Carica le classi del plugin
        $this->load_classes();
        
        // Inizializza le classi
        $this->init_classes();
    }
    
    /**
     * Carica i file di traduzione
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            'custom-product-variants',
            false,
            dirname(CPV_PLUGIN_BASENAME) . '/languages/'
        );
    }
    
    /**
     * Verifica se WooCommerce è attivo
     */
    private function is_woocommerce_active() {
        return class_exists('WooCommerce');
    }
    
    /**
     * Controlla se WooCommerce è attivo e mostra un avviso se non lo è
     */
    public function check_woocommerce() {
        if (!$this->is_woocommerce_active()) {
            add_action('admin_notices', array($this, 'woocommerce_missing_notice'));
            deactivate_plugins(CPV_PLUGIN_BASENAME);
        }
    }
    
    /**
     * Mostra un avviso se WooCommerce non è attivo
     */
    public function woocommerce_missing_notice() {
        ?>
        <div class="notice notice-error">
            <p><?php _e('Il plugin "Varianti Prodotto Personalizzate" richiede WooCommerce per funzionare. Per favore installa e attiva WooCommerce.', 'custom-product-variants'); ?></p>
        </div>
        <?php
    }
    
    /**
     * Carica le classi del plugin
     */
    private function load_classes() {
        require_once CPV_PLUGIN_DIR . 'includes/class-database.php';
        require_once CPV_PLUGIN_DIR . 'includes/class-admin.php';
        require_once CPV_PLUGIN_DIR . 'includes/class-frontend.php';
        require_once CPV_PLUGIN_DIR . 'includes/class-cart-integration.php';
        require_once CPV_PLUGIN_DIR . 'includes/class-settings.php';
    }
    
    /**
     * Inizializza le istanze delle classi
     */
    private function init_classes() {
        $this->database = new CPV_Database();
        $this->settings = CPV_Settings::get_instance();
        $this->admin = new CPV_Admin();
        $this->frontend = new CPV_Frontend();
        $this->cart_integration = new CPV_Cart_Integration();
    }
    
    /**
     * Attivazione del plugin
     */
    public function activate() {
        // Verifica se WooCommerce è attivo
        if (!$this->is_woocommerce_active()) {
            wp_die(__('Il plugin "Varianti Prodotto Personalizzate" richiede WooCommerce per funzionare. Per favore installa e attiva WooCommerce prima di attivare questo plugin.', 'custom-product-variants'));
        }

        // Carica la classe database se non è già caricata
        if (!class_exists('CPV_Database')) {
            require_once CPV_PLUGIN_DIR . 'includes/class-database.php';
        }

        // Crea le tabelle del database
        $database = new CPV_Database();
        $database->create_tables();

        // Verifica che la tabella sia stata creata
        if (!$database->table_exists()) {
            error_log('CPV Activation Error: Failed to create database table');
            wp_die(__('Errore durante la creazione della tabella del database. Controlla i permessi del database.', 'custom-product-variants'));
        }

        // Salva la versione del plugin
        update_option('cpv_plugin_version', CPV_PLUGIN_VERSION);

        // Log di attivazione
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV Plugin activated successfully');
        }

        // Flush delle regole di rewrite
        flush_rewrite_rules();
    }
    
    /**
     * Disattivazione del plugin
     */
    public function deactivate() {
        // Flush delle regole di rewrite
        flush_rewrite_rules();
    }
}

/**
 * Inizializza il plugin
 */
function cpv_init() {
    return Custom_Product_Variants::get_instance();
}

// Avvia il plugin
cpv_init();
