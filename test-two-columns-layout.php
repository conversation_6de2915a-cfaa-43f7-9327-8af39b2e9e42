<?php
/**
 * Test per verificare il nuovo layout "two-columns"
 * 
 * Questo file può essere utilizzato per testare rapidamente
 * il nuovo layout a due colonne senza dover configurare
 * un ambiente WooCommerce completo.
 */

// Impedisce l'accesso diretto
if (!defined('ABSPATH')) {
    // Per test standalone
    define('ABSPATH', dirname(__FILE__) . '/');
}

?>
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Layout Due Colonne - Custom Product Variants</title>
    <link rel="stylesheet" href="assets/css/frontend.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #0073aa;
            padding-bottom: 10px;
        }
        .layout-demo {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Test Layout Due Colonne - Custom Product Variants</h1>
        <p>Questo test mostra come appare il nuovo layout "two-columns" per le varianti di prodotto.</p>

        <!-- Test Layout Due Colonne -->
        <div class="test-section">
            <h2>Layout Due Colonne (Desktop)</h2>
            <div class="cpv-product-variants-container cpv-layout-two-columns">
                <h3 class="cpv-variants-title">
                    Varianti Disponibili
                    <span class="cpv-variants-count">(4)</span>
                </h3>
                <p class="cpv-variants-description">Seleziona una variante per personalizzare il tuo acquisto:</p>

                <div class="cpv-variants-grid cpv-layout-two-columns">
                    <!-- Variante 1 -->
                    <div class="cpv-variant-card cpv-layout-two-columns" data-variant-id="1" data-variant-price="29.99">
                        <div class="cpv-variant-image">
                            <div class="cpv-no-image-placeholder">
                                <span class="cpv-no-image-icon">📷</span>
                            </div>
                        </div>
                        <div class="cpv-variant-content">
                            <div class="cpv-variant-info">
                                <h4 class="cpv-variant-name">Variante Rossa</h4>
                                <div class="cpv-variant-price">€29.99</div>
                            </div>
                            <div class="cpv-variant-select">
                                <input type="radio" name="cpv_selected_variant" id="cpv_variant_1" value="1" class="cpv-variant-radio">
                                <label for="cpv_variant_1" class="cpv-variant-label">Seleziona</label>
                            </div>
                        </div>
                    </div>

                    <!-- Variante 2 -->
                    <div class="cpv-variant-card cpv-layout-two-columns" data-variant-id="2" data-variant-price="34.99">
                        <div class="cpv-variant-image">
                            <div class="cpv-no-image-placeholder">
                                <span class="cpv-no-image-icon">📷</span>
                            </div>
                        </div>
                        <div class="cpv-variant-content">
                            <div class="cpv-variant-info">
                                <h4 class="cpv-variant-name">Variante Blu</h4>
                                <div class="cpv-variant-price">€34.99</div>
                            </div>
                            <div class="cpv-variant-select">
                                <input type="radio" name="cpv_selected_variant" id="cpv_variant_2" value="2" class="cpv-variant-radio">
                                <label for="cpv_variant_2" class="cpv-variant-label">Seleziona</label>
                            </div>
                        </div>
                    </div>

                    <!-- Variante 3 -->
                    <div class="cpv-variant-card cpv-layout-two-columns" data-variant-id="3" data-variant-price="39.99">
                        <div class="cpv-variant-image">
                            <div class="cpv-no-image-placeholder">
                                <span class="cpv-no-image-icon">📷</span>
                            </div>
                        </div>
                        <div class="cpv-variant-content">
                            <div class="cpv-variant-info">
                                <h4 class="cpv-variant-name">Variante Verde</h4>
                                <div class="cpv-variant-price">€39.99</div>
                            </div>
                            <div class="cpv-variant-select">
                                <input type="radio" name="cpv_selected_variant" id="cpv_variant_3" value="3" class="cpv-variant-radio">
                                <label for="cpv_variant_3" class="cpv-variant-label">Seleziona</label>
                            </div>
                        </div>
                    </div>

                    <!-- Variante 4 -->
                    <div class="cpv-variant-card cpv-layout-two-columns" data-variant-id="4" data-variant-price="44.99">
                        <div class="cpv-variant-image">
                            <div class="cpv-no-image-placeholder">
                                <span class="cpv-no-image-icon">📷</span>
                            </div>
                        </div>
                        <div class="cpv-variant-content">
                            <div class="cpv-variant-info">
                                <h4 class="cpv-variant-name">Variante Gialla</h4>
                                <div class="cpv-variant-price">€44.99</div>
                            </div>
                            <div class="cpv-variant-select">
                                <input type="radio" name="cpv_selected_variant" id="cpv_variant_4" value="4" class="cpv-variant-radio">
                                <label for="cpv_variant_4" class="cpv-variant-label">Seleziona</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Confronto con Layout Verticale -->
        <div class="test-section">
            <h2>Confronto: Layout Verticale (Originale)</h2>
            <div class="cpv-product-variants-container cpv-layout-vertical">
                <h3 class="cpv-variants-title">
                    Varianti Disponibili
                    <span class="cpv-variants-count">(2)</span>
                </h3>
                <p class="cpv-variants-description">Layout originale per confronto:</p>

                <div class="cpv-variants-grid cpv-layout-vertical">
                    <!-- Variante 1 -->
                    <div class="cpv-variant-card cpv-layout-vertical" data-variant-id="5" data-variant-price="29.99">
                        <div class="cpv-variant-image">
                            <div class="cpv-no-image-placeholder">
                                <span class="cpv-no-image-icon">📷</span>
                            </div>
                        </div>
                        <div class="cpv-variant-content">
                            <div class="cpv-variant-info">
                                <h4 class="cpv-variant-name">Variante Verticale 1</h4>
                                <div class="cpv-variant-price">€29.99</div>
                            </div>
                            <div class="cpv-variant-select">
                                <input type="radio" name="cpv_selected_variant_vertical" id="cpv_variant_5" value="5" class="cpv-variant-radio">
                                <label for="cpv_variant_5" class="cpv-variant-label">Seleziona</label>
                            </div>
                        </div>
                    </div>

                    <!-- Variante 2 -->
                    <div class="cpv-variant-card cpv-layout-vertical" data-variant-id="6" data-variant-price="34.99">
                        <div class="cpv-variant-image">
                            <div class="cpv-no-image-placeholder">
                                <span class="cpv-no-image-icon">📷</span>
                            </div>
                        </div>
                        <div class="cpv-variant-content">
                            <div class="cpv-variant-info">
                                <h4 class="cpv-variant-name">Variante Verticale 2</h4>
                                <div class="cpv-variant-price">€34.99</div>
                            </div>
                            <div class="cpv-variant-select">
                                <input type="radio" name="cpv_selected_variant_vertical" id="cpv_variant_6" value="6" class="cpv-variant-radio">
                                <label for="cpv_variant_6" class="cpv-variant-label">Seleziona</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Istruzioni -->
        <div class="test-section">
            <h2>Come utilizzare il nuovo layout</h2>
            <ol>
                <li>Vai nel pannello di amministrazione di WordPress</li>
                <li>Naviga su <strong>Custom Product Variants → Impostazioni</strong></li>
                <li>Nella sezione "Impostazioni Layout", seleziona <strong>"Due Colonne"</strong></li>
                <li>Salva le impostazioni</li>
                <li>Ora le varianti dei prodotti verranno visualizzate con esattamente due varianti per riga</li>
            </ol>
            
            <h3>Caratteristiche del layout "Due Colonne":</h3>
            <ul>
                <li>✅ Esattamente due varianti per riga su desktop</li>
                <li>✅ Layout responsive: diventa una colonna su mobile (≤768px)</li>
                <li>✅ Mantiene due colonne su tablet (769px-1024px)</li>
                <li>✅ Stessi effetti hover e interazioni degli altri layout</li>
                <li>✅ Compatibile con tutte le funzionalità esistenti</li>
            </ul>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="assets/js/frontend.js"></script>
</body>
</html>
