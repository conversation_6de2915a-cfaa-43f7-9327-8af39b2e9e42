/**
 * Stili CSS per il frontend del plugin Custom Product Variants
 */

/* Container principale delle varianti */
.cpv-product-variants-container {
    margin: 30px 0;
    padding: 25px;
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.cpv-variants-title {
    margin: 0 0 15px 0;
    font-size: 24px;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.cpv-variants-count {
    font-size: 16px;
    font-weight: 400;
    color: #666;
    background: #f0f0f0;
    padding: 4px 8px;
    border-radius: 12px;
}

.cpv-variants-description {
    margin: 0 0 25px 0;
    color: #666;
    font-size: 16px;
    line-height: 1.5;
}

/* Griglia delle varianti */
.cpv-variants-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 25px;
}

/* Layout Verticale (default) */
.cpv-variants-grid.cpv-layout-vertical {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* Layout Orizzontale */
.cpv-variants-grid.cpv-layout-horizontal {
    grid-template-columns: 1fr;
    gap: 15px;
}

/* Layout Due Colonne */
.cpv-variants-grid.cpv-layout-two-columns {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

/* Card singola variante */
.cpv-variant-card {
    background: #fff;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    display: flex;
}

/* Layout Verticale */
.cpv-variant-card.cpv-layout-vertical {
    flex-direction: column;
    text-align: center;
}

/* Layout Orizzontale */
.cpv-variant-card.cpv-layout-horizontal {
    flex-direction: row;
    align-items: center;
    text-align: left;
    max-width: 100%;
}

/* Layout Due Colonne */
.cpv-variant-card.cpv-layout-two-columns {
    flex-direction: column;
    text-align: center;
}

/* Effetti hover (solo se abilitati) */
.cpv-variant-card:hover {
    border-color: #0073aa;
    box-shadow: 0 4px 12px rgba(0,115,170,0.15);
    transform: translateY(-2px);
}

/* Disabilita effetti hover se richiesto */
.cpv-no-hover-effects .cpv-variant-card:hover {
    border-color: #e1e1e1;
    box-shadow: none;
    transform: none;
}

.cpv-no-hover-effects .cpv-variant-card:hover .cpv-variant-image img {
    transform: none;
}

.cpv-variant-card.selected {
    border-color: #0073aa;
    background: #f0f8ff;
    box-shadow: 0 4px 12px rgba(0,115,170,0.2);
}

.cpv-variant-card.selected::before {
    content: "✓";
    position: absolute;
    top: 10px;
    right: 10px;
    background: #0073aa;
    color: #fff;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
}

/* Immagine variante */
.cpv-variant-image {
    border-radius: 6px;
    overflow: hidden;
    background: #f8f8f8;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

/* Layout Verticale - Immagine */
.cpv-layout-vertical .cpv-variant-image {
    width: 100%;
    height: 200px;
    margin-bottom: 15px;
}

/* Layout Orizzontale - Immagine */
.cpv-layout-horizontal .cpv-variant-image {
    width: 120px;
    height: 120px;
    margin-right: 20px;
    margin-bottom: 0;
}

/* Layout Due Colonne - Immagine */
.cpv-layout-two-columns .cpv-variant-image {
    width: 100%;
    height: 180px;
    margin-bottom: 15px;
}

.cpv-variant-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.cpv-variant-card:hover .cpv-variant-image img {
    transform: scale(1.05);
}

.cpv-no-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    color: #999;
    font-size: 48px;
}

/* Container contenuto variante */
.cpv-variant-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* Layout Orizzontale - Content */
.cpv-layout-horizontal .cpv-variant-content {
    justify-content: space-between;
}

/* Informazioni variante */
.cpv-variant-info {
    margin-bottom: 15px;
}

/* Layout Verticale - Info */
.cpv-layout-vertical .cpv-variant-info {
    text-align: center;
}

/* Layout Orizzontale - Info */
.cpv-layout-horizontal .cpv-variant-info {
    text-align: left;
    flex: 1;
}

.cpv-variant-name {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.cpv-variant-price {
    font-size: 20px;
    font-weight: 700;
    color: #0073aa;
    margin: 0;
}

/* Selezione variante */
.cpv-variant-select {
    margin-top: auto;
}

/* Layout Verticale - Select */
.cpv-layout-vertical .cpv-variant-select {
    text-align: center;
}

/* Layout Orizzontale - Select */
.cpv-layout-horizontal .cpv-variant-select {
    text-align: right;
    margin-left: 15px;
    display: flex;
    align-items: center;
}

/* Layout Due Colonne - Select */
.cpv-layout-two-columns .cpv-variant-select {
    text-align: center;
}

.cpv-variant-radio {
    display: none;
}

.cpv-variant-label {
    display: inline-block;
    padding: 12px 24px;
    background: #0073aa;
    color: #fff;
    border-radius: 25px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #0073aa;
}

.cpv-variant-label:hover {
    background: #005a87;
    border-color: #005a87;
    transform: translateY(-1px);
}

.cpv-variant-radio:checked + .cpv-variant-label {
    background: #28a745;
    border-color: #28a745;
}

.cpv-variant-radio:checked + .cpv-variant-label:hover {
    background: #218838;
    border-color: #218838;
}

/* Informazioni selezione */
.cpv-variant-selection-info {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    margin-top: 20px;
}

.cpv-selected-variant-text {
    margin: 0 0 15px 0;
    font-size: 16px;
    color: #333;
}

.cpv-selected-variant-name {
    color: #0073aa;
    font-weight: 600;
}

.cpv-selected-variant-price {
    color: #28a745;
    font-weight: 700;
}

.cpv-clear-selection {
    background: #6c757d !important;
    border-color: #6c757d !important;
    color: #fff !important;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
}

.cpv-clear-selection:hover {
    background: #5a6268 !important;
    border-color: #5a6268 !important;
}

/* Informazioni variante nel carrello */
.cpv-variant-info {
    font-size: 13px;
    color: #666;
    font-style: italic;
}

/* Loading state */
.cpv-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.cpv-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0073aa;
    border-radius: 50%;
    animation: cpv-spin 1s linear infinite;
}

@keyframes cpv-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Messaggi di errore/successo */
.cpv-message {
    padding: 12px 16px;
    margin: 15px 0;
    border-radius: 4px;
    font-size: 14px;
}

.cpv-message-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cpv-message-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cpv-message-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Responsive Design */
/* Tablet - mantiene due colonne per il layout two-columns */
@media (max-width: 1024px) and (min-width: 769px) {
    .cpv-variants-grid.cpv-layout-two-columns {
        gap: 15px;
    }

    .cpv-layout-two-columns .cpv-variant-image {
        height: 160px;
    }
}

@media (max-width: 768px) {
    .cpv-product-variants-container {
        margin: 20px 0;
        padding: 20px;
    }

    .cpv-variants-title {
        font-size: 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .cpv-variants-description {
        font-size: 14px;
    }

    .cpv-variants-grid.cpv-layout-vertical,
    .cpv-variants-grid.cpv-layout-horizontal,
    .cpv-variants-grid.cpv-layout-two-columns {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .cpv-variant-card {
        padding: 15px;
    }

    /* Layout orizzontale diventa verticale su mobile */
    .cpv-variant-card.cpv-layout-horizontal {
        flex-direction: column;
        text-align: center;
    }

    .cpv-layout-horizontal .cpv-variant-image {
        width: 100%;
        height: 150px;
        margin-right: 0;
        margin-bottom: 15px;
    }

    .cpv-layout-horizontal .cpv-variant-info {
        text-align: center;
    }

    .cpv-layout-horizontal .cpv-variant-select {
        text-align: center;
        margin-left: 0;
        margin-top: 15px;
    }

    .cpv-layout-vertical .cpv-variant-image {
        height: 150px;
    }

    /* Layout due colonne mantiene la struttura verticale su mobile */
    .cpv-layout-two-columns .cpv-variant-image {
        height: 150px;
    }

    .cpv-variant-name {
        font-size: 16px;
    }

    .cpv-variant-price {
        font-size: 18px;
    }

    .cpv-variant-label {
        padding: 10px 20px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .cpv-product-variants-container {
        padding: 15px;
    }
    
    .cpv-variants-title {
        font-size: 18px;
    }
    
    .cpv-variant-card {
        padding: 12px;
    }
    
    .cpv-variant-image {
        height: 120px;
    }
    
    .cpv-variant-name {
        font-size: 14px;
    }
    
    .cpv-variant-price {
        font-size: 16px;
    }
    
    .cpv-variant-label {
        padding: 8px 16px;
        font-size: 12px;
    }
    
    .cpv-variant-selection-info {
        padding: 15px;
    }
    
    .cpv-selected-variant-text {
        font-size: 14px;
    }
}

/* Integrazione con temi WooCommerce */
.woocommerce .cpv-product-variants-container {
    margin: 30px 0;
}

.woocommerce .cpv-variant-card {
    font-family: inherit;
}

.woocommerce .cpv-variant-price {
    font-family: inherit;
}

/* Accessibilità */
.cpv-variant-card:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.cpv-variant-label:focus {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

/* Animazioni */
.cpv-variant-card {
    animation: cpv-fadeIn 0.5s ease-in-out;
}

@keyframes cpv-fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
