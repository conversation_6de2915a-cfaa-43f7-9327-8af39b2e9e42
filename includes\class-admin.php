<?php
/**
 * Classe per la gestione del pannello amministrativo
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

class CPV_Admin {
    
    /**
     * Istanza della classe Database
     */
    private $database;
    
    /**
     * Costruttore
     */
    public function __construct() {
        $this->database = new CPV_Database();
        $this->init_hooks();
    }
    
    /**
     * Inizializza gli hook per l'admin
     */
    private function init_hooks() {
        // Hook per aggiungere il tab alle pagine prodotto
        add_filter('woocommerce_product_data_tabs', array($this, 'add_product_data_tab'));
        add_action('woocommerce_product_data_panels', array($this, 'add_product_data_panel'));
        
        // Hook per salvare i dati del prodotto
        add_action('woocommerce_process_product_meta', array($this, 'save_product_data'));
        
        // Hook per caricare gli script e stili admin
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Hook per le chiamate AJAX
        add_action('wp_ajax_cpv_add_variant', array($this, 'ajax_add_variant'));
        add_action('wp_ajax_cpv_update_variant', array($this, 'ajax_update_variant'));
        add_action('wp_ajax_cpv_delete_variant', array($this, 'ajax_delete_variant'));
        add_action('wp_ajax_cpv_get_variants', array($this, 'ajax_get_variants'));
        add_action('wp_ajax_cpv_update_variants_order', array($this, 'ajax_update_variants_order'));
        add_action('wp_ajax_cpv_toggle_variant', array($this, 'ajax_toggle_variant'));

        // Hook per l'upload delle immagini
        add_action('wp_ajax_cpv_upload_variant_image', array($this, 'ajax_upload_variant_image'));
    }
    
    /**
     * Aggiunge il tab "Varianti Prodotto" alla pagina di modifica prodotto
     */
    public function add_product_data_tab($tabs) {
        $tabs['cpv_variants'] = array(
            'label'    => __('Varianti Prodotto', 'custom-product-variants'),
            'target'   => 'cpv_variants_data',
            'class'    => array('show_if_simple', 'show_if_variable'),
            'priority' => 25,
        );
        
        return $tabs;
    }
    
    /**
     * Aggiunge il pannello del tab "Varianti Prodotto"
     */
    public function add_product_data_panel() {
        global $post;

        error_log('CPV: add_product_data_panel called');

        $product_id = $post->ID;
        error_log('CPV: Product ID: ' . $product_id);

        try {
            $variants = $this->database->get_product_variants($product_id, false);
            error_log('CPV: Variants retrieved: ' . count($variants));
        } catch (Exception $e) {
            error_log('CPV: Error getting variants: ' . $e->getMessage());
            $variants = array();
        }
        
        ?>
        <div id="cpv_variants_data" class="panel woocommerce_options_panel">
            <div class="cpv-variants-container">
                <div class="cpv-variants-header">
                    <h3><?php _e('Gestione Varianti Prodotto', 'custom-product-variants'); ?></h3>
                    <p class="description">
                        <?php _e('Crea e gestisci varianti personalizzate per questo prodotto. Le varianti saranno visualizzate nella pagina prodotto e potranno essere acquistate al posto del prodotto principale.', 'custom-product-variants'); ?>
                    </p>
                </div>
                
                <div class="cpv-add-variant-section">
                    <h4><?php _e('Aggiungi Nuova Variante', 'custom-product-variants'); ?></h4>
                    <?php error_log('CPV: About to render form div (no form tag)'); ?>
                    <div id="cpv-add-variant-form" class="cpv-variant-form">
                        <?php
                        error_log('CPV: Form div rendered, generating nonce');
                        wp_nonce_field('cpv_add_variant', 'cpv_add_variant_nonce');
                        error_log('CPV: Nonce generated successfully');
                        ?>
                        <input type="hidden" name="product_id" value="<?php echo esc_attr($product_id); ?>">

                        <div class="cpv-form-row">
                            <div class="cpv-form-field cpv-field-name">
                                <label for="cpv_variant_name"><?php _e('Nome Variante', 'custom-product-variants'); ?> <span class="required">*</span></label>
                                <input type="text" id="cpv_variant_name" name="variant_name" class="regular-text" maxlength="255" required placeholder="<?php _e('Inserisci il nome della variante', 'custom-product-variants'); ?>">
                                <p class="description"><?php _e('Nome della variante (massimo 255 caratteri)', 'custom-product-variants'); ?></p>
                            </div>

                            <div class="cpv-form-field cpv-field-price">
                                <label for="cpv_variant_price"><?php _e('Prezzo Variante', 'custom-product-variants'); ?> <span class="required">*</span></label>
                                <div class="cpv-price-input-wrapper">
                                    <input type="number" id="cpv_variant_price" name="variant_price" class="small-text" step="0.01" min="0" required placeholder="0.00">
                                    <span class="currency-symbol"><?php echo get_woocommerce_currency_symbol(); ?></span>
                                </div>
                                <p class="description"><?php _e('Prezzo della variante (deve essere maggiore di 0)', 'custom-product-variants'); ?></p>
                            </div>
                        </div>

                        <div class="cpv-form-row">
                            <div class="cpv-form-field cpv-field-image">
                                <label for="cpv_variant_image"><?php _e('Immagine Variante', 'custom-product-variants'); ?></label>
                                <div class="cpv-image-upload">
                                    <input type="hidden" id="cpv_variant_image" name="variant_image" value="">
                                    <div class="cpv-image-preview" style="display: none;">
                                        <img src="" alt="" style="max-width: 150px; height: auto; border-radius: 4px;">
                                        <div class="cpv-image-actions">
                                            <button type="button" class="cpv-remove-image button"><?php _e('Rimuovi', 'custom-product-variants'); ?></button>
                                        </div>
                                    </div>
                                    <button type="button" class="cpv-upload-image button"><?php _e('Carica Immagine', 'custom-product-variants'); ?></button>
                                    <p class="description"><?php _e('Immagine opzionale per la variante', 'custom-product-variants'); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="cpv-form-actions">
                            <button type="button" class="button button-primary cpv-submit-btn">
                                <span class="cpv-btn-text"><?php _e('Aggiungi Variante', 'custom-product-variants'); ?></span>
                                <span class="cpv-loading" style="display: none;"><?php _e('Caricamento...', 'custom-product-variants'); ?></span>
                            </button>
                            <div class="cpv-form-messages"></div>
                        </div>
                    </div>
                </div>
                
                <div class="cpv-variants-list-section">
                    <h4><?php _e('Varianti Esistenti', 'custom-product-variants'); ?></h4>
                    <div id="cpv-variants-list">
                        <?php if (empty($variants)): ?>
                            <p class="cpv-no-variants"><?php _e('Nessuna variante creata per questo prodotto.', 'custom-product-variants'); ?></p>
                        <?php else: ?>
                            <?php $this->render_variants_list($variants); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Renderizza la lista delle varianti esistenti
     */
    private function render_variants_list($variants) {
        ?>
        <div class="cpv-variants-table-container">
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th class="cpv-col-image"><?php _e('Immagine', 'custom-product-variants'); ?></th>
                        <th class="cpv-col-name"><?php _e('Nome', 'custom-product-variants'); ?></th>
                        <th class="cpv-col-price"><?php _e('Prezzo', 'custom-product-variants'); ?></th>
                        <th class="cpv-col-status"><?php _e('Stato', 'custom-product-variants'); ?></th>
                        <th class="cpv-col-actions"><?php _e('Azioni', 'custom-product-variants'); ?></th>
                    </tr>
                </thead>
                <tbody id="cpv-variants-tbody" class="cpv-sortable">
                    <?php foreach ($variants as $variant): ?>
                        <tr data-variant-id="<?php echo esc_attr($variant->id); ?>">
                            <td class="cpv-col-image">
                                <?php if ($variant->variant_image): ?>
                                    <img src="<?php echo esc_url($variant->variant_image); ?>" alt="<?php echo esc_attr($variant->variant_name); ?>" style="width: 50px; height: 50px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="cpv-no-image"><?php _e('Nessuna immagine', 'custom-product-variants'); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="cpv-col-name">
                                <strong><?php echo esc_html($variant->variant_name); ?></strong>
                            </td>
                            <td class="cpv-col-price">
                                <?php echo wc_price($variant->variant_price); ?>
                            </td>
                            <td class="cpv-col-status">
                                <span class="cpv-status cpv-status-<?php echo $variant->is_active ? 'active' : 'inactive'; ?>">
                                    <?php echo $variant->is_active ? __('Attiva', 'custom-product-variants') : __('Inattiva', 'custom-product-variants'); ?>
                                </span>
                            </td>
                            <td class="cpv-col-actions">
                                <button type="button" class="button button-small cpv-edit-variant" data-variant-id="<?php echo esc_attr($variant->id); ?>">
                                    <?php _e('Modifica', 'custom-product-variants'); ?>
                                </button>
                                <button type="button" class="button button-small cpv-toggle-variant" data-variant-id="<?php echo esc_attr($variant->id); ?>" data-active="<?php echo esc_attr($variant->is_active); ?>">
                                    <?php echo $variant->is_active ? __('Disattiva', 'custom-product-variants') : __('Attiva', 'custom-product-variants'); ?>
                                </button>
                                <button type="button" class="button button-small cpv-delete-variant" data-variant-id="<?php echo esc_attr($variant->id); ?>">
                                    <?php _e('Elimina', 'custom-product-variants'); ?>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php
    }
    
    /**
     * Carica gli script e stili per l'admin
     */
    public function enqueue_admin_scripts($hook) {
        // Debug: Log dell'hook
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV Admin Scripts Hook: ' . $hook);
        }

        // Carica solo nelle pagine di modifica prodotto
        if ($hook !== 'post.php' && $hook !== 'post-new.php') {
            return;
        }

        global $post, $typenow;

        // Controlla sia $post che $typenow per essere sicuri
        $is_product_page = false;
        if ($post && $post->post_type === 'product') {
            $is_product_page = true;
        } elseif ($typenow === 'product') {
            $is_product_page = true;
        } elseif (isset($_GET['post_type']) && $_GET['post_type'] === 'product') {
            $is_product_page = true;
        } elseif (isset($_GET['post']) && get_post_type($_GET['post']) === 'product') {
            $is_product_page = true;
        }

        if (!$is_product_page) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('CPV Admin Scripts: Not a product page. Post type: ' . ($post ? $post->post_type : 'no post') . ', typenow: ' . $typenow);
            }
            return;
        }

        // Debug: Conferma caricamento
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV Admin Scripts: Loading scripts for product page');
        }
        
        // Carica gli stili CSS
        wp_enqueue_style(
            'cpv-admin-css',
            CPV_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            CPV_PLUGIN_VERSION
        );
        
        // Carica gli script JavaScript
        wp_enqueue_script(
            'cpv-admin-js',
            CPV_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery', 'jquery-ui-sortable'),
            CPV_PLUGIN_VERSION,
            true
        );
        
        // Carica il media uploader di WordPress
        wp_enqueue_media();
        
        // Localizza lo script con le variabili necessarie
        wp_localize_script('cpv-admin-js', 'cpv_admin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('cpv_admin_nonce'),
            'strings' => array(
                'confirm_delete' => __('Sei sicuro di voler eliminare questa variante?', 'custom-product-variants'),
                'error_generic' => __('Si è verificato un errore. Riprova.', 'custom-product-variants'),
                'success_added' => __('Variante aggiunta con successo!', 'custom-product-variants'),
                'success_updated' => __('Variante aggiornata con successo!', 'custom-product-variants'),
                'success_deleted' => __('Variante eliminata con successo!', 'custom-product-variants'),
                'loading' => __('Caricamento...', 'custom-product-variants'),
            )
        ));

        // Aggiungi script inline per debug
        wp_add_inline_script('cpv-admin-js', '
            console.log("CPV Admin script enqueued");
            console.log("CPV Admin data:", cpv_admin);

            // Fallback per assicurarsi che il form sia gestito
            jQuery(document).ready(function($) {
                console.log("CPV Fallback script loaded");

                // Verifica che il form esista
                if ($("#cpv-add-variant-form").length > 0) {
                    console.log("CPV Form found in fallback");

                    // Rimuovi eventuali handler precedenti e aggiungi il nostro
                    $("#cpv-add-variant-form").off("submit").on("submit", function(e) {
                        console.log("CPV Fallback form submit");
                        e.preventDefault();

                        // Qui puoi aggiungere la logica AJAX di fallback
                        alert("Form intercettato! Controlla la console per i dettagli.");
                        return false;
                    });
                } else {
                    console.log("CPV Form NOT found in fallback");
                }
            });
        ');
    }
    
    /**
     * Salva i dati del prodotto (hook di WooCommerce)
     */
    public function save_product_data($post_id) {
        // Questo metodo può essere utilizzato per salvare dati aggiuntivi
        // se necessario, ma la maggior parte delle operazioni viene gestita via AJAX
    }

    /**
     * AJAX: Aggiunge una nuova variante
     */
    public function ajax_add_variant() {
        // Debug: Log della richiesta
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV Add Variant AJAX called with data: ' . print_r($_POST, true));
        }

        // Verifica il nonce
        if (!wp_verify_nonce($_POST['cpv_add_variant_nonce'], 'cpv_add_variant')) {
            error_log('CPV Error: Nonce verification failed');
            wp_send_json_error(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica i permessi (temporaneamente disabilitato per debug)
        if (!current_user_can('edit_products') && !defined('CPV_DEBUG_MODE')) {
            error_log('CPV Error: User does not have edit_products capability. User ID: ' . get_current_user_id());
            wp_send_json_error(__('Non hai i permessi per eseguire questa azione. Devi essere loggato come amministratore.', 'custom-product-variants'));
        }

        // Sanitizza e valida i dati
        $product_id = intval($_POST['product_id']);
        $variant_name = sanitize_text_field($_POST['variant_name']);
        $variant_price = floatval($_POST['variant_price']);
        $variant_image = isset($_POST['variant_image']) ? esc_url_raw($_POST['variant_image']) : '';

        // Debug: Log dei dati processati
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV Processed data: product_id=' . $product_id . ', name=' . $variant_name . ', price=' . $variant_price . ', image=' . $variant_image);
        }

        // Validazione
        if (empty($variant_name) || $variant_price <= 0) {
            error_log('CPV Error: Validation failed - name: ' . $variant_name . ', price: ' . $variant_price);
            wp_send_json_error(__('Nome e prezzo della variante sono obbligatori e il prezzo deve essere maggiore di 0.', 'custom-product-variants'));
        }

        // Verifica che la tabella esista
        if (!$this->database->table_exists()) {
            error_log('CPV Error: Database table does not exist');
            wp_send_json_error(__('Errore database: tabella non trovata.', 'custom-product-variants'));
        }

        // Aggiunge la variante al database
        $variant_id = $this->database->add_variant($product_id, $variant_name, $variant_image, $variant_price);

        if (is_wp_error($variant_id)) {
            error_log('CPV Error: Database insert failed - ' . $variant_id->get_error_message());
            wp_send_json_error($variant_id->get_error_message());
        }

        if (!$variant_id) {
            error_log('CPV Error: Database insert returned false');
            wp_send_json_error(__('Errore durante il salvataggio della variante.', 'custom-product-variants'));
        }

        // Ottiene la variante appena creata
        $variant = $this->database->get_variant($variant_id);

        if (!$variant) {
            error_log('CPV Error: Could not retrieve created variant with ID: ' . $variant_id);
            wp_send_json_error(__('Errore durante il recupero della variante creata.', 'custom-product-variants'));
        }

        // Debug: Log del successo
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('CPV Success: Variant created with ID: ' . $variant_id);
        }

        wp_send_json_success(array(
            'message' => __('Variante aggiunta con successo!', 'custom-product-variants'),
            'variant' => $variant
        ));
    }

    /**
     * AJAX: Aggiorna una variante esistente
     */
    public function ajax_update_variant() {
        // Verifica il nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_admin_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica i permessi
        if (!current_user_can('edit_products')) {
            wp_die(__('Non hai i permessi per eseguire questa azione.', 'custom-product-variants'));
        }

        // Sanitizza e valida i dati
        $variant_id = intval($_POST['variant_id']);
        $variant_name = sanitize_text_field($_POST['variant_name']);
        $variant_price = floatval($_POST['variant_price']);
        $variant_image = esc_url_raw($_POST['variant_image']);

        // Validazione
        if (empty($variant_name) || $variant_price <= 0) {
            wp_send_json_error(__('Nome e prezzo della variante sono obbligatori e il prezzo deve essere maggiore di 0.', 'custom-product-variants'));
        }

        // Aggiorna la variante nel database
        $result = $this->database->update_variant($variant_id, $variant_name, $variant_image, $variant_price);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        // Ottiene la variante aggiornata
        $variant = $this->database->get_variant($variant_id);

        wp_send_json_success(array(
            'message' => __('Variante aggiornata con successo!', 'custom-product-variants'),
            'variant' => $variant
        ));
    }

    /**
     * AJAX: Elimina una variante
     */
    public function ajax_delete_variant() {
        // Verifica il nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_admin_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica i permessi
        if (!current_user_can('edit_products')) {
            wp_die(__('Non hai i permessi per eseguire questa azione.', 'custom-product-variants'));
        }

        $variant_id = intval($_POST['variant_id']);

        // Elimina la variante dal database
        $result = $this->database->delete_variant($variant_id);

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success(array(
            'message' => __('Variante eliminata con successo!', 'custom-product-variants')
        ));
    }

    /**
     * AJAX: Ottiene le varianti di un prodotto
     */
    public function ajax_get_variants() {
        // Verifica il nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_admin_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica i permessi
        if (!current_user_can('edit_products')) {
            wp_die(__('Non hai i permessi per eseguire questa azione.', 'custom-product-variants'));
        }

        $product_id = intval($_POST['product_id']);
        $variants = $this->database->get_product_variants($product_id, false);

        wp_send_json_success($variants);
    }

    /**
     * AJAX: Aggiorna l'ordine delle varianti
     */
    public function ajax_update_variants_order() {
        // Verifica il nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_admin_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica i permessi
        if (!current_user_can('edit_products')) {
            wp_die(__('Non hai i permessi per eseguire questa azione.', 'custom-product-variants'));
        }

        $variants_order = $_POST['variants_order'];

        if (!is_array($variants_order)) {
            wp_send_json_error(__('Dati non validi.', 'custom-product-variants'));
        }

        $result = $this->database->update_variants_order($variants_order);

        if (!$result) {
            wp_send_json_error(__('Errore durante l\'aggiornamento dell\'ordine.', 'custom-product-variants'));
        }

        wp_send_json_success(array(
            'message' => __('Ordine aggiornato con successo!', 'custom-product-variants')
        ));
    }

    /**
     * AJAX: Attiva/disattiva una variante
     */
    public function ajax_toggle_variant() {
        // Verifica il nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_admin_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica i permessi
        if (!current_user_can('edit_products')) {
            wp_die(__('Non hai i permessi per eseguire questa azione.', 'custom-product-variants'));
        }

        $variant_id = intval($_POST['variant_id']);
        $is_active = intval($_POST['is_active']);

        // Aggiorna lo stato della variante
        $result = $this->database->toggle_variant_status($variant_id, $is_active);

        if (!$result) {
            wp_send_json_error(__('Errore durante l\'aggiornamento dello stato.', 'custom-product-variants'));
        }

        wp_send_json_success(array(
            'message' => __('Stato variante aggiornato con successo!', 'custom-product-variants')
        ));
    }

    /**
     * AJAX: Upload immagine variante
     */
    public function ajax_upload_variant_image() {
        // Verifica il nonce
        if (!wp_verify_nonce($_POST['nonce'], 'cpv_admin_nonce')) {
            wp_die(__('Errore di sicurezza.', 'custom-product-variants'));
        }

        // Verifica i permessi
        if (!current_user_can('upload_files')) {
            wp_die(__('Non hai i permessi per caricare file.', 'custom-product-variants'));
        }

        // Gestisce l'upload del file
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }

        $uploadedfile = $_FILES['file'];
        $upload_overrides = array('test_form' => false);

        $movefile = wp_handle_upload($uploadedfile, $upload_overrides);

        if ($movefile && !isset($movefile['error'])) {
            wp_send_json_success(array(
                'url' => $movefile['url'],
                'message' => __('Immagine caricata con successo!', 'custom-product-variants')
            ));
        } else {
            wp_send_json_error($movefile['error']);
        }
    }
}
