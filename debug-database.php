<?php
/**
 * File di debug per verificare lo stato del database
 * Accedi a: /wp-content/plugins/custom-product-variants/debug-database.php
 */

// Carica WordPress
require_once('../../../wp-load.php');

// Verifica che l'utente sia amministratore
if (!current_user_can('manage_options')) {
    wp_die('Non hai i permessi per accedere a questa pagina.');
}

// Carica la classe database
require_once('includes/class-database.php');

$database = new CPV_Database();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Debug Database - Custom Product Variants</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Debug Database - Custom Product Variants</h1>
    
    <h2>1. Verifica Esistenza Tabella</h2>
    <?php
    $table_exists = $database->table_exists();
    if ($table_exists) {
        echo '<p class="success">✓ La tabella esiste</p>';
    } else {
        echo '<p class="error">✗ La tabella NON esiste</p>';
        echo '<p>Tentativo di creazione tabella...</p>';
        $database->create_tables();
        
        // Ricontrolla
        $table_exists = $database->table_exists();
        if ($table_exists) {
            echo '<p class="success">✓ Tabella creata con successo</p>';
        } else {
            echo '<p class="error">✗ Impossibile creare la tabella</p>';
        }
    }
    ?>
    
    <h2>2. Informazioni Tabella</h2>
    <?php
    $table_info = $database->get_table_info();
    if (isset($table_info['error'])) {
        echo '<p class="error">Errore: ' . $table_info['error'] . '</p>';
    } else {
        echo '<p class="info">Nome tabella: ' . $table_info['table_name'] . '</p>';
        echo '<p class="info">Numero righe: ' . $table_info['row_count'] . '</p>';
        
        if (!empty($table_info['columns'])) {
            echo '<h3>Struttura Colonne:</h3>';
            echo '<table>';
            echo '<tr><th>Campo</th><th>Tipo</th><th>Null</th><th>Chiave</th><th>Default</th></tr>';
            foreach ($table_info['columns'] as $column) {
                echo '<tr>';
                echo '<td>' . $column->Field . '</td>';
                echo '<td>' . $column->Type . '</td>';
                echo '<td>' . $column->Null . '</td>';
                echo '<td>' . $column->Key . '</td>';
                echo '<td>' . $column->Default . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
    }
    ?>
    
    <h2>3. Test Inserimento Variante</h2>
    <?php
    if ($table_exists) {
        // Test di inserimento
        $test_product_id = 1; // ID prodotto di test
        $test_name = 'Variante Test ' . date('H:i:s');
        $test_price = 19.99;
        $test_image = '';
        
        echo '<p>Tentativo inserimento variante di test...</p>';
        echo '<p>Prodotto ID: ' . $test_product_id . '</p>';
        echo '<p>Nome: ' . $test_name . '</p>';
        echo '<p>Prezzo: ' . $test_price . '</p>';
        
        $variant_id = $database->add_variant($test_product_id, $test_name, $test_image, $test_price);
        
        if (is_wp_error($variant_id)) {
            echo '<p class="error">✗ Errore inserimento: ' . $variant_id->get_error_message() . '</p>';
        } elseif ($variant_id) {
            echo '<p class="success">✓ Variante inserita con ID: ' . $variant_id . '</p>';
            
            // Recupera la variante
            $variant = $database->get_variant($variant_id);
            if ($variant) {
                echo '<h4>Dati variante inserita:</h4>';
                echo '<pre>' . print_r($variant, true) . '</pre>';
                
                // Elimina la variante di test
                $deleted = $database->delete_variant($variant_id);
                if ($deleted) {
                    echo '<p class="info">Variante di test eliminata</p>';
                }
            }
        } else {
            echo '<p class="error">✗ Inserimento fallito (nessun ID restituito)</p>';
        }
    }
    ?>
    
    <h2>4. Varianti Esistenti</h2>
    <?php
    if ($table_exists) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'cpv_product_variants';
        $variants = $wpdb->get_results("SELECT * FROM {$table_name} ORDER BY id DESC LIMIT 10");
        
        if (empty($variants)) {
            echo '<p class="info">Nessuna variante trovata nel database</p>';
        } else {
            echo '<p class="info">Ultime 10 varianti:</p>';
            echo '<table>';
            echo '<tr><th>ID</th><th>Prodotto ID</th><th>Nome</th><th>Prezzo</th><th>Attiva</th><th>Creata</th></tr>';
            foreach ($variants as $variant) {
                echo '<tr>';
                echo '<td>' . $variant->id . '</td>';
                echo '<td>' . $variant->product_id . '</td>';
                echo '<td>' . $variant->variant_name . '</td>';
                echo '<td>€' . $variant->variant_price . '</td>';
                echo '<td>' . ($variant->is_active ? 'Sì' : 'No') . '</td>';
                echo '<td>' . $variant->created_at . '</td>';
                echo '</tr>';
            }
            echo '</table>';
        }
    }
    ?>
    
    <h2>5. Informazioni WordPress</h2>
    <?php
    global $wpdb;
    echo '<p><strong>Database Host:</strong> ' . DB_HOST . '</p>';
    echo '<p><strong>Database Name:</strong> ' . DB_NAME . '</p>';
    echo '<p><strong>Table Prefix:</strong> ' . $wpdb->prefix . '</p>';
    echo '<p><strong>WordPress Version:</strong> ' . get_bloginfo('version') . '</p>';
    echo '<p><strong>WooCommerce Active:</strong> ' . (class_exists('WooCommerce') ? 'Sì' : 'No') . '</p>';
    echo '<p><strong>Current User ID:</strong> ' . get_current_user_id() . '</p>';
    echo '<p><strong>Current User Can Edit Products:</strong> ' . (current_user_can('edit_products') ? 'Sì' : 'No') . '</p>';
    ?>
    
    <h2>6. Log Errori</h2>
    <?php
    $log_file = WP_CONTENT_DIR . '/debug.log';
    if (file_exists($log_file)) {
        $log_content = file_get_contents($log_file);
        $cpv_logs = array();
        $lines = explode("\n", $log_content);
        
        foreach ($lines as $line) {
            if (strpos($line, 'CPV') !== false) {
                $cpv_logs[] = $line;
            }
        }
        
        if (!empty($cpv_logs)) {
            echo '<p class="info">Ultimi log CPV (max 20):</p>';
            echo '<pre>';
            $recent_logs = array_slice($cpv_logs, -20);
            foreach ($recent_logs as $log) {
                echo htmlspecialchars($log) . "\n";
            }
            echo '</pre>';
        } else {
            echo '<p class="info">Nessun log CPV trovato</p>';
        }
    } else {
        echo '<p class="info">File di log non trovato: ' . $log_file . '</p>';
    }
    ?>
    
    <hr>
    <p><small>Debug eseguito il: <?php echo date('Y-m-d H:i:s'); ?></small></p>
    
</body>
</html>
