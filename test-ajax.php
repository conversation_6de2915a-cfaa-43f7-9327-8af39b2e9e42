<?php
/**
 * File di test per verificare il funzionamento AJAX
 * Accedi a: http://test.test/wp-content/plugins/custom-product-variants/test-ajax.php
 */

// Carica WordPress
require_once('../../../wp-load.php');

// Abilita modalità debug per il test
define('CPV_DEBUG_MODE', true);

// Per il test, saltiamo la verifica dei permessi
// if (!current_user_can('manage_options')) {
//     wp_die('Non hai i permessi per accedere a questa pagina.');
// }

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test AJAX - Custom Product Variants</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="number"] { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { padding: 10px 20px; background: #0073aa; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #005a87; }
        .result { margin-top: 20px; padding: 15px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>Test AJAX - Custom Product Variants</h1>
    
    <h2>1. Test Aggiunta Variante</h2>
    <form id="test-form">
        <?php wp_nonce_field('cpv_add_variant', 'cpv_add_variant_nonce'); ?>
        
        <div class="form-group">
            <label for="product_id">Product ID:</label>
            <input type="number" id="product_id" name="product_id" value="1" required>
        </div>
        
        <div class="form-group">
            <label for="variant_name">Nome Variante:</label>
            <input type="text" id="variant_name" name="variant_name" value="Variante Test" required>
        </div>
        
        <div class="form-group">
            <label for="variant_price">Prezzo Variante:</label>
            <input type="number" id="variant_price" name="variant_price" step="0.01" value="19.99" required>
        </div>
        
        <div class="form-group">
            <label for="variant_image">Immagine URL (opzionale):</label>
            <input type="text" id="variant_image" name="variant_image" value="">
        </div>
        
        <button type="submit">Aggiungi Variante</button>
    </form>
    
    <div id="result"></div>
    
    <h2>2. Informazioni Debug</h2>
    <div class="info">
        <p><strong>AJAX URL:</strong> <?php echo admin_url('admin-ajax.php'); ?></p>
        <p><strong>Current User ID:</strong> <?php echo get_current_user_id(); ?></p>
        <p><strong>Can Edit Products:</strong> <?php echo current_user_can('edit_products') ? 'Sì' : 'No'; ?></p>
        <p><strong>WooCommerce Active:</strong> <?php echo class_exists('WooCommerce') ? 'Sì' : 'No'; ?></p>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        console.log('Test AJAX script loaded');
        
        $('#test-form').on('submit', function(e) {
            e.preventDefault();
            console.log('Form submitted');
            
            var $form = $(this);
            var $result = $('#result');
            var $button = $form.find('button');
            
            // Disabilita il pulsante
            $button.prop('disabled', true).text('Caricamento...');
            
            // Prepara i dati
            var formData = new FormData($form[0]);
            formData.append('action', 'cpv_add_variant');
            
            console.log('Sending AJAX request...');
            
            // Invia la richiesta AJAX
            $.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    console.log('AJAX Success:', response);
                    
                    if (response.success) {
                        $result.html('<div class="success">✓ Variante aggiunta con successo!<br>ID: ' + response.data.variant.id + '</div>');
                    } else {
                        $result.html('<div class="error">✗ Errore: ' + (response.data || 'Errore sconosciuto') + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX Error:', xhr, status, error);
                    $result.html('<div class="error">✗ Errore AJAX: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText + '</div>');
                },
                complete: function() {
                    $button.prop('disabled', false).text('Aggiungi Variante');
                }
            });
        });
    });
    </script>
    
</body>
</html>
