# Changelog - Layout Due Colonne

## Modifiche Implementate

### Nuova Funzionalità: Layout "Due Colonne"

È stato aggiunto un nuovo layout chiamato "Due Colonne" che permette di visualizzare esattamente **due varianti per riga** nel frontend.

### File Modificati

#### 1. `includes/class-settings.php`
- **Riga 37**: Aggiornato commento per includere `two-columns`
- **Righe 254-279**: Aggiunta nuova opzione di layout nel pannello amministrativo
- **Riga 402**: Aggiornata validazione per accettare `two-columns`
- **Riga 523**: Aggiornata validazione AJAX per `two-columns`

#### 2. `assets/css/frontend.css`
- **Righe 61-65**: Aggiunto CSS per griglia due colonne
- **Righe 94-98**: Aggiunto CSS per card nel layout due colonne
- **Righe 165-171**: Aggiunto CSS per immagini nel layout due colonne
- **Righe 255-260**: Aggiunto CSS per selezione nel layout due colonne
- **Righe 399-407**: Aggiunta media query per tablet
- **Righe 428-431**: Aggiornata media query mobile per includere due colonne
- **Righe 454-456**: Aggiunto CSS responsive per immagini due colonne

#### 3. `assets/css/settings.css`
- **Righe 104-124**: Aggiunto CSS per anteprima layout due colonne nel pannello admin

#### 4. `assets/js/settings.js`
- **Righe 73-115**: Aggiornata funzione `generatePreviewHtml` per gestire layout due colonne

### Nuovi File

#### 1. `test-two-columns-layout.php`
File di test standalone per verificare il funzionamento del nuovo layout senza necessità di configurare WooCommerce.

## Come Utilizzare

### Per gli Amministratori
1. Accedi al pannello di amministrazione WordPress
2. Vai su **Custom Product Variants → Impostazioni**
3. Nella sezione "Impostazioni Layout", seleziona **"Due Colonne"**
4. Salva le impostazioni

### Per i Sviluppatori
Il nuovo layout è completamente integrato nel sistema esistente:

```php
// Il layout viene automaticamente applicato quando impostato
$layout_style = $this->settings->get_setting('layout_style', 'vertical');
// Ora può essere: 'vertical', 'horizontal', o 'two-columns'
```

## Caratteristiche Tecniche

### CSS Grid
```css
.cpv-variants-grid.cpv-layout-two-columns {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}
```

### Responsive Design
- **Desktop (>1024px)**: Due colonne
- **Tablet (769px-1024px)**: Due colonne con gap ridotto
- **Mobile (≤768px)**: Una colonna

### Compatibilità
- ✅ Compatibile con tutti i browser moderni
- ✅ Supporta effetti hover esistenti
- ✅ Mantiene tutte le funzionalità JavaScript
- ✅ Responsive design completo
- ✅ Accessibilità mantenuta

## Test

Per testare il nuovo layout:

1. **Test Standalone**: Apri `test-two-columns-layout.php` nel browser
2. **Test Integrato**: Configura il layout nel pannello admin e visualizza una pagina prodotto con varianti

## Retrocompatibilità

Tutte le modifiche sono completamente retrocompatibili:
- I layout esistenti (`vertical` e `horizontal`) continuano a funzionare normalmente
- Nessuna modifica ai database esistenti
- Nessuna modifica alle API esistenti

## Struttura HTML Generata

```html
<div class="cpv-variants-grid cpv-layout-two-columns">
    <div class="cpv-variant-card cpv-layout-two-columns">
        <!-- Contenuto variante 1 -->
    </div>
    <div class="cpv-variant-card cpv-layout-two-columns">
        <!-- Contenuto variante 2 -->
    </div>
    <!-- Riga successiva -->
    <div class="cpv-variant-card cpv-layout-two-columns">
        <!-- Contenuto variante 3 -->
    </div>
    <div class="cpv-variant-card cpv-layout-two-columns">
        <!-- Contenuto variante 4 -->
    </div>
</div>
```

## Note per il Futuro

Se in futuro si volessero aggiungere altri layout (es. tre colonne, quattro colonne), il sistema è già predisposto:

1. Aggiungere la nuova opzione in `class-settings.php`
2. Aggiungere i CSS corrispondenti in `frontend.css`
3. Aggiornare le funzioni di validazione
4. Aggiungere l'anteprima in `settings.css` e `settings.js`

Il sistema è modulare e facilmente estensibile.
