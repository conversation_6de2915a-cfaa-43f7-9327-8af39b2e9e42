/**
 * JavaScript per il frontend del plugin Custom Product Variants
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Variabili globali
    var selectedVariant = null;
    var originalPrice = null;
    var $priceElement = null;
    var $addToCartForm = null;
    
    /**
     * Inizializzazione
     */
    function init() {
        // Verifica se ci sono varianti nella pagina
        if ($('.cpv-product-variants-container').length === 0) {
            return;
        }
        
        // Trova gli elementi del prezzo e del form
        findPriceElements();
        findAddToCartForm();
        
        // Salva il prezzo originale
        saveOriginalPrice();
        
        // Associa gli eventi
        bindEvents();
        
        // Inizializza lo stato
        initializeState();
    }
    
    /**
     * Trova gli elementi del prezzo nella pagina
     */
    function findPriceElements() {
        // Cerca gli elementi comuni del prezzo in WooCommerce
        $priceElement = $('.woocommerce-Price-amount, .price, .woocommerce-price-amount').first();
        
        if ($priceElement.length === 0) {
            $priceElement = $('.summary .price').first();
        }
    }
    
    /**
     * Trova il form di aggiunta al carrello
     */
    function findAddToCartForm() {
        $addToCartForm = $('form.cart, .cart form').first();
    }
    
    /**
     * Salva il prezzo originale del prodotto
     */
    function saveOriginalPrice() {
        if (typeof cpv_product_data !== 'undefined' && cpv_product_data.original_price) {
            originalPrice = cpv_product_data.original_price;
        } else if ($priceElement.length > 0) {
            // Estrae il prezzo dal testo (rimuove simboli di valuta e formattazione)
            var priceText = $priceElement.text().replace(/[^\d.,]/g, '');
            originalPrice = parseFloat(priceText.replace(',', '.'));
        }
    }
    
    /**
     * Associa gli eventi
     */
    function bindEvents() {
        // Selezione variante
        $(document).on('change', '.cpv-variant-radio', handleVariantSelection);
        $(document).on('click', '.cpv-variant-card', handleVariantCardClick);
        
        // Rimozione selezione
        $(document).on('click', '.cpv-clear-selection', handleClearSelection);
        
        // Intercetta il submit del form di aggiunta al carrello
        if ($addToCartForm.length > 0) {
            $addToCartForm.on('submit', handleAddToCart);
        }
        
        // Hover effects
        $(document).on('mouseenter', '.cpv-variant-card', handleCardHover);
        $(document).on('mouseleave', '.cpv-variant-card', handleCardLeave);
    }
    
    /**
     * Inizializza lo stato della pagina
     */
    function initializeState() {
        // Nasconde le informazioni di selezione inizialmente
        $('.cpv-variant-selection-info').hide();
        
        // Aggiunge animazioni alle card
        $('.cpv-variant-card').each(function(index) {
            $(this).css('animation-delay', (index * 0.1) + 's');
        });
    }
    
    /**
     * Gestisce la selezione di una variante tramite radio button
     */
    function handleVariantSelection(e) {
        var $radio = $(this);
        var $card = $radio.closest('.cpv-variant-card');
        var variantId = $radio.val();
        
        selectVariant(variantId, $card);
    }
    
    /**
     * Gestisce il click su una card variante
     */
    function handleVariantCardClick(e) {
        // Evita il doppio trigger se si clicca sul radio button
        if ($(e.target).hasClass('cpv-variant-radio') || $(e.target).hasClass('cpv-variant-label')) {
            return;
        }
        
        var $card = $(this);
        var $radio = $card.find('.cpv-variant-radio');
        
        // Seleziona il radio button
        $radio.prop('checked', true).trigger('change');
    }
    
    /**
     * Seleziona una variante
     */
    function selectVariant(variantId, $card) {
        // Rimuove la selezione precedente
        $('.cpv-variant-card').removeClass('selected');
        
        // Aggiunge la classe selected alla card corrente
        $card.addClass('selected');
        
        // Salva la variante selezionata
        selectedVariant = {
            id: variantId,
            name: $card.find('.cpv-variant-name').text(),
            price: parseFloat($card.data('variant-price')),
            card: $card
        };
        
        // Aggiorna il prezzo visualizzato
        updateDisplayedPrice(selectedVariant.price);
        
        // Mostra le informazioni di selezione
        showSelectionInfo();
        
        // Trigger evento personalizzato
        $(document).trigger('cpv_variant_selected', [selectedVariant]);
    }
    
    /**
     * Gestisce la rimozione della selezione
     */
    function handleClearSelection(e) {
        e.preventDefault();
        clearSelection();
    }
    
    /**
     * Rimuove la selezione corrente
     */
    function clearSelection() {
        // Rimuove la selezione visuale
        $('.cpv-variant-card').removeClass('selected');
        $('.cpv-variant-radio').prop('checked', false);
        
        // Reset della variante selezionata
        selectedVariant = null;
        
        // Ripristina il prezzo originale
        updateDisplayedPrice(originalPrice);
        
        // Nasconde le informazioni di selezione
        hideSelectionInfo();
        
        // Trigger evento personalizzato
        $(document).trigger('cpv_variant_cleared');
    }
    
    /**
     * Aggiorna il prezzo visualizzato
     */
    function updateDisplayedPrice(price) {
        if ($priceElement.length === 0 || !price) {
            return;
        }
        
        // Formatta il prezzo usando AJAX per ottenere la formattazione corretta
        $.ajax({
            url: cpv_frontend.ajax_url,
            type: 'POST',
            data: {
                action: 'cpv_format_price',
                price: price,
                nonce: cpv_frontend.nonce
            },
            success: function(response) {
                if (response.success && response.data.formatted_price) {
                    $priceElement.html(response.data.formatted_price);
                }
            }
        });
        
        // Fallback: aggiorna con formattazione semplice
        var currencySymbol = '€'; // Può essere personalizzato
        var formattedPrice = currencySymbol + price.toFixed(2);
        $priceElement.html(formattedPrice);
    }
    
    /**
     * Mostra le informazioni di selezione
     */
    function showSelectionInfo() {
        if (!selectedVariant) {
            return;
        }
        
        var $info = $('.cpv-variant-selection-info');
        var $name = $info.find('.cpv-selected-variant-name');
        var $price = $info.find('.cpv-selected-variant-price');
        
        $name.text(selectedVariant.name);
        $price.text('€' + selectedVariant.price.toFixed(2));
        
        $info.slideDown(300);
    }
    
    /**
     * Nasconde le informazioni di selezione
     */
    function hideSelectionInfo() {
        $('.cpv-variant-selection-info').slideUp(300);
    }
    
    /**
     * Gestisce l'aggiunta al carrello
     */
    function handleAddToCart(e) {
        // Se è selezionata una variante, aggiunge il campo nascosto al form
        if (selectedVariant) {
            // Rimuove eventuali campi precedenti
            $addToCartForm.find('input[name="cpv_selected_variant"]').remove();
            
            // Aggiunge il campo con la variante selezionata
            $addToCartForm.append(
                '<input type="hidden" name="cpv_selected_variant" value="' + selectedVariant.id + '">'
            );
        }
        
        // Continua con il submit normale
        return true;
    }
    
    /**
     * Gestisce l'hover sulle card
     */
    function handleCardHover(e) {
        var $card = $(this);
        
        if (!$card.hasClass('selected')) {
            $card.addClass('hover');
        }
    }
    
    /**
     * Gestisce l'uscita dall'hover sulle card
     */
    function handleCardLeave(e) {
        var $card = $(this);
        $card.removeClass('hover');
    }
    
    /**
     * Mostra un messaggio di errore
     */
    function showError(message) {
        var $error = $('<div class="cpv-message cpv-message-error">' + message + '</div>');
        $('.cpv-product-variants-container').prepend($error);
        
        setTimeout(function() {
            $error.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }
    
    /**
     * Mostra un messaggio di successo
     */
    function showSuccess(message) {
        var $success = $('<div class="cpv-message cpv-message-success">' + message + '</div>');
        $('.cpv-product-variants-container').prepend($success);
        
        setTimeout(function() {
            $success.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }
    
    /**
     * Ottiene la variante attualmente selezionata
     */
    function getSelectedVariant() {
        return selectedVariant;
    }
    
    /**
     * Seleziona una variante tramite ID
     */
    function selectVariantById(variantId) {
        var $radio = $('.cpv-variant-radio[value="' + variantId + '"]');
        if ($radio.length > 0) {
            $radio.prop('checked', true).trigger('change');
        }
    }
    
    /**
     * Verifica se una variante è selezionata
     */
    function hasSelectedVariant() {
        return selectedVariant !== null;
    }
    
    // API pubblica
    window.CPV_Frontend = {
        getSelectedVariant: getSelectedVariant,
        selectVariantById: selectVariantById,
        clearSelection: clearSelection,
        hasSelectedVariant: hasSelectedVariant
    };
    
    // Inizializza quando il documento è pronto
    init();
    
    // Reinizializza dopo aggiornamenti AJAX (compatibilità con temi)
    $(document).on('updated_wc_div', function() {
        setTimeout(init, 100);
    });
});
