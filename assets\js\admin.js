/**
 * JavaScript per il pannello amministrativo del plugin Custom Product Variants
 */

jQuery(document).ready(function($) {
    'use strict';

    // Debug: Conferma caricamento script
    console.log('CPV Admin Script loaded');

    // Verifica che cpv_admin sia disponibile
    if (typeof cpv_admin === 'undefined') {
        console.error('CPV Error: cpv_admin object not found');
        return;
    }

    console.log('CPV Admin data:', cpv_admin);

    // Variabili globali
    var mediaUploader;
    var currentEditingVariant = null;

    /**
     * Inizializzazione
     */
    function init() {
        console.log('CPV Admin: Initializing...');
        bindEvents();
        initSortable();

        // Ascolta i cambiamenti dei tab per reinizializzare quando necessario
        $(document).on('click', '.wc-tabs li a', function() {
            var tabId = $(this).attr('href');
            if (tabId === '#cpv_variants_data') {
                console.log('CPV Admin: Variants tab activated, reinitializing...');
                // Aspetta che il contenuto del tab sia visibile
                setTimeout(function() {
                    bindFormEvents();
                }, 100);
            }
        });

        console.log('CPV Admin: Initialized');
    }

    /**
     * Associa gli eventi generali
     */
    function bindEvents() {
        console.log('CPV Admin: Binding events...');

        // Prova subito a trovare il form
        bindFormEvents();

        // Upload immagine (usa delegazione eventi)
        $(document).on('click', '.cpv-upload-image', handleImageUpload);
        $(document).on('click', '.cpv-remove-image', handleImageRemove);

        // Azioni varianti (usa delegazione eventi)
        $(document).on('click', '.cpv-edit-variant', handleEditVariant);
        $(document).on('click', '.cpv-delete-variant', handleDeleteVariant);
        $(document).on('click', '.cpv-toggle-variant', handleToggleVariant);

        // Validazione form in tempo reale (usa delegazione eventi)
        $(document).on('input', '#cpv_variant_name', validateVariantName);
        $(document).on('input', '#cpv_variant_price', validateVariantPrice);

        console.log('CPV Admin: Events bound');
    }

    /**
     * Binding specifico per il form (può essere chiamato più volte)
     */
    function bindFormEvents() {
        // Verifica che il form div esista (ora è un div, non un form)
        var $form = $('#cpv-add-variant-form');
        console.log('CPV Form div found:', $form.length > 0);

        if ($form.length > 0) {
            // Rimuovi eventuali handler precedenti per evitare duplicati
            $(document).off('click.cpv', '.cpv-submit-btn');

            // Button aggiunta variante (non più submit form)
            $(document).on('click.cpv', '.cpv-submit-btn', function(e) {
                console.log('CPV Button click triggered');
                handleAddVariant.call(this, e);
            });
            console.log('CPV Button click handler attached');
        } else {
            console.warn('CPV Warning: Form div #cpv-add-variant-form not found');
        }
    }
    
    /**
     * Inizializza il sortable per riordinare le varianti
     */
    function initSortable() {
        $('#cpv-variants-tbody').sortable({
            handle: 'tr',
            placeholder: 'ui-sortable-placeholder',
            helper: 'clone',
            update: function(event, ui) {
                updateVariantsOrder();
            }
        });
    }
    
    /**
     * Gestisce l'aggiunta di una nuova variante
     */
    function handleAddVariant(e) {
        console.log('CPV handleAddVariant called');
        e.preventDefault();

        var $btn = $(this);  // Ora 'this' è il button, non il form
        var $form = $btn.closest('#cpv-add-variant-form');  // Trova il form div
        var $btnText = $btn.find('.cpv-btn-text');
        var $loading = $btn.find('.cpv-loading');
        var $messages = $form.find('.cpv-form-messages');

        console.log('CPV Form elements found:', {
            btn: $btn.length,
            form: $form.length,
            btnText: $btnText.length,
            loading: $loading.length,
            messages: $messages.length
        });

        // Pulisce i messaggi precedenti
        $messages.empty();
        clearFormErrors($form);

        // Validazione
        if (!validateForm($form)) {
            return false;
        }

        // Disabilita il button e mostra loading
        $btn.prop('disabled', true);
        $btnText.hide();
        $loading.show();

        // Prepara i dati manualmente (non possiamo usare FormData su un div)
        var formData = new FormData();
        formData.append('action', 'cpv_add_variant');
        formData.append('cpv_add_variant_nonce', $form.find('input[name="cpv_add_variant_nonce"]').val());
        formData.append('product_id', $form.find('input[name="product_id"]').val());
        formData.append('variant_name', $form.find('input[name="variant_name"]').val());
        formData.append('variant_price', $form.find('input[name="variant_price"]').val());

        // Aggiungi URL immagine se presente (dal WordPress Media Uploader)
        var imageUrl = $form.find('input[name="variant_image"]').val();
        if (imageUrl) {
            formData.append('variant_image', imageUrl);
        }

        // Invia la richiesta AJAX
        $.ajax({
            url: cpv_admin.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showFormMessage($messages, cpv_admin.strings.success_added, 'success');
                    resetForm($form);
                    addVariantToList(response.data.variant);
                    updateEmptyState();
                } else {
                    showFormMessage($messages, response.data || cpv_admin.strings.error_generic, 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                showFormMessage($messages, cpv_admin.strings.error_generic, 'error');
            },
            complete: function() {
                $btn.prop('disabled', false);
                $btnText.show();
                $loading.hide();
            }
        });
    }
    
    /**
     * Gestisce l'upload delle immagini
     */
    function handleImageUpload(e) {
        e.preventDefault();
        
        var $button = $(this);
        var $container = $button.closest('.cpv-image-upload');
        var $input = $container.find('input[type="hidden"]');
        var $preview = $container.find('.cpv-image-preview');
        
        // Se il media uploader esiste già, aprilo
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }
        
        // Crea il media uploader
        mediaUploader = wp.media({
            title: 'Seleziona Immagine Variante',
            button: {
                text: 'Usa questa immagine'
            },
            multiple: false,
            library: {
                type: 'image'
            }
        });
        
        // Quando viene selezionata un'immagine
        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            
            $input.val(attachment.url);
            $preview.find('img').attr('src', attachment.url);
            $preview.show();
            $button.text('Cambia Immagine');
        });
        
        // Apri il media uploader
        mediaUploader.open();
    }
    
    /**
     * Gestisce la rimozione delle immagini
     */
    function handleImageRemove(e) {
        e.preventDefault();
        
        var $button = $(this);
        var $container = $button.closest('.cpv-image-upload');
        var $input = $container.find('input[type="hidden"]');
        var $preview = $container.find('.cpv-image-preview');
        var $uploadBtn = $container.find('.cpv-upload-image');
        
        $input.val('');
        $preview.hide();
        $uploadBtn.text('Carica Immagine');
    }
    
    /**
     * Gestisce la modifica di una variante
     */
    function handleEditVariant(e) {
        e.preventDefault();
        
        var $button = $(this);
        var variantId = $button.data('variant-id');
        
        // Implementa la logica di modifica
        // Per ora mostra un alert, ma può essere esteso con un modal
        alert('Funzionalità di modifica in sviluppo. ID Variante: ' + variantId);
    }
    
    /**
     * Gestisce l'eliminazione di una variante
     */
    function handleDeleteVariant(e) {
        e.preventDefault();
        
        var $button = $(this);
        var variantId = $button.data('variant-id');
        
        if (!confirm(cpv_admin.strings.confirm_delete)) {
            return;
        }
        
        $button.prop('disabled', true).text(cpv_admin.strings.loading);
        
        $.ajax({
            url: cpv_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'cpv_delete_variant',
                variant_id: variantId,
                nonce: cpv_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice(cpv_admin.strings.success_deleted, 'success');
                    $button.closest('tr').fadeOut(300, function() {
                        $(this).remove();
                        checkEmptyList();
                    });
                } else {
                    showNotice(response.data || cpv_admin.strings.error_generic, 'error');
                    $button.prop('disabled', false).text('Elimina');
                }
            },
            error: function() {
                showNotice(cpv_admin.strings.error_generic, 'error');
                $button.prop('disabled', false).text('Elimina');
            }
        });
    }
    
    /**
     * Gestisce l'attivazione/disattivazione di una variante
     */
    function handleToggleVariant(e) {
        e.preventDefault();
        
        var $button = $(this);
        var variantId = $button.data('variant-id');
        var isActive = $button.data('active');
        var newStatus = isActive ? 0 : 1;
        
        $button.prop('disabled', true).text(cpv_admin.strings.loading);
        
        $.ajax({
            url: cpv_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'cpv_toggle_variant',
                variant_id: variantId,
                is_active: newStatus,
                nonce: cpv_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    var $row = $button.closest('tr');
                    var $status = $row.find('.cpv-status');
                    
                    if (newStatus) {
                        $status.removeClass('cpv-status-inactive').addClass('cpv-status-active').text('Attiva');
                        $button.text('Disattiva').data('active', 1);
                    } else {
                        $status.removeClass('cpv-status-active').addClass('cpv-status-inactive').text('Inattiva');
                        $button.text('Attiva').data('active', 0);
                    }
                    
                    showNotice('Stato variante aggiornato con successo!', 'success');
                } else {
                    showNotice(response.data || cpv_admin.strings.error_generic, 'error');
                }
            },
            error: function() {
                showNotice(cpv_admin.strings.error_generic, 'error');
            },
            complete: function() {
                $button.prop('disabled', false);
            }
        });
    }
    
    /**
     * Aggiorna l'ordine delle varianti
     */
    function updateVariantsOrder() {
        var variantsOrder = [];
        
        $('#cpv-variants-tbody tr').each(function(index) {
            var variantId = $(this).data('variant-id');
            if (variantId) {
                variantsOrder.push(variantId);
            }
        });
        
        $.ajax({
            url: cpv_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'cpv_update_variants_order',
                variants_order: variantsOrder,
                nonce: cpv_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    showNotice('Ordine aggiornato con successo!', 'success');
                }
            }
        });
    }
    
    /**
     * Validazione del nome variante
     */
    function validateVariantName() {
        var $input = $(this);
        var value = $input.val().trim();
        var $error = $input.siblings('.cpv-error');
        
        if (value.length === 0) {
            showFieldError($input, 'Il nome della variante è obbligatorio.');
            return false;
        } else if (value.length > 255) {
            showFieldError($input, 'Il nome della variante non può superare i 255 caratteri.');
            return false;
        } else {
            hideFieldError($input);
            return true;
        }
    }
    
    /**
     * Validazione del prezzo variante
     */
    function validateVariantPrice() {
        var $input = $(this);
        var value = parseFloat($input.val());
        
        if (isNaN(value) || value <= 0) {
            showFieldError($input, 'Il prezzo deve essere un numero maggiore di 0.');
            return false;
        } else {
            hideFieldError($input);
            return true;
        }
    }
    
    /**
     * Validazione completa del form
     */
    function validateForm($form) {
        var isValid = true;
        
        // Valida nome
        var $nameInput = $form.find('#cpv_variant_name');
        if (!validateVariantName.call($nameInput[0])) {
            isValid = false;
        }
        
        // Valida prezzo
        var $priceInput = $form.find('#cpv_variant_price');
        if (!validateVariantPrice.call($priceInput[0])) {
            isValid = false;
        }
        
        return isValid;
    }
    
    /**
     * Mostra errore campo
     */
    function showFieldError($input, message) {
        var $error = $input.siblings('.cpv-error');
        if ($error.length === 0) {
            $error = $('<span class="cpv-error" style="color: #d63638; font-size: 12px; display: block; margin-top: 5px;"></span>');
            $input.after($error);
        }
        $error.text(message);
        $input.addClass('cpv-input-error');
    }
    
    /**
     * Nasconde errore campo
     */
    function hideFieldError($input) {
        $input.siblings('.cpv-error').remove();
        $input.removeClass('cpv-input-error');
    }
    
    /**
     * Mostra messaggio nel form
     */
    function showFormMessage($container, message, type) {
        var $message = $('<div class="cpv-form-message cpv-form-message-' + type + '">' + message + '</div>');
        $container.html($message);

        setTimeout(function() {
            $message.fadeOut(300, function() {
                $(this).remove();
            });
        }, type === 'success' ? 3000 : 5000);
    }

    /**
     * Mostra notifica globale
     */
    function showNotice(message, type) {
        var $notice = $('<div class="cpv-notice cpv-notice-' + type + '">' + message + '</div>');
        $('.cpv-variants-container').prepend($notice);

        setTimeout(function() {
            $notice.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }

    /**
     * Aggiunge una variante alla lista senza ricaricare
     */
    function addVariantToList(variant) {
        var $tbody = $('#cpv-variants-tbody');
        var $noVariants = $('.cpv-no-variants');

        // Se non esiste la tabella, creala
        if ($tbody.length === 0) {
            createVariantsTable();
            $tbody = $('#cpv-variants-tbody');
        }

        // Rimuovi il messaggio "nessuna variante"
        $noVariants.remove();

        // Crea la riga della nuova variante
        var $row = createVariantRow(variant);
        $tbody.append($row);

        // Anima l'aggiunta
        $row.hide().fadeIn(500);

        // Reinizializza il sortable
        initSortable();
    }

    /**
     * Crea la tabella delle varianti se non esiste
     */
    function createVariantsTable() {
        var tableHtml = `
            <div class="cpv-variants-table-container">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th class="cpv-col-image">Immagine</th>
                            <th class="cpv-col-name">Nome</th>
                            <th class="cpv-col-price">Prezzo</th>
                            <th class="cpv-col-status">Stato</th>
                            <th class="cpv-col-actions">Azioni</th>
                        </tr>
                    </thead>
                    <tbody id="cpv-variants-tbody" class="cpv-sortable">
                    </tbody>
                </table>
            </div>
        `;

        $('#cpv-variants-list').html(tableHtml);
    }

    /**
     * Crea una riga per una variante
     */
    function createVariantRow(variant) {
        var imageHtml = variant.variant_image ?
            '<img src="' + variant.variant_image + '" alt="' + variant.variant_name + '" style="width: 50px; height: 50px; object-fit: cover;">' :
            '<div class="cpv-no-image">Nessuna immagine</div>';

        var statusClass = variant.is_active == 1 ? 'active' : 'inactive';
        var statusText = variant.is_active == 1 ? 'Attiva' : 'Inattiva';
        var toggleText = variant.is_active == 1 ? 'Disattiva' : 'Attiva';

        var rowHtml = `
            <tr data-variant-id="${variant.id}">
                <td class="cpv-col-image">${imageHtml}</td>
                <td class="cpv-col-name"><strong>${variant.variant_name}</strong></td>
                <td class="cpv-col-price">${formatPrice(variant.variant_price)}</td>
                <td class="cpv-col-status">
                    <span class="cpv-status cpv-status-${statusClass}">${statusText}</span>
                </td>
                <td class="cpv-col-actions">
                    <button type="button" class="button button-small cpv-edit-variant" data-variant-id="${variant.id}">Modifica</button>
                    <button type="button" class="button button-small cpv-toggle-variant" data-variant-id="${variant.id}" data-active="${variant.is_active}">${toggleText}</button>
                    <button type="button" class="button button-small cpv-delete-variant" data-variant-id="${variant.id}">Elimina</button>
                </td>
            </tr>
        `;

        return $(rowHtml);
    }

    /**
     * Formatta il prezzo (semplice fallback)
     */
    function formatPrice(price) {
        return '€' + parseFloat(price).toFixed(2);
    }

    /**
     * Aggiorna lo stato vuoto
     */
    function updateEmptyState() {
        var $tbody = $('#cpv-variants-tbody');
        var $list = $('#cpv-variants-list');

        if ($tbody.find('tr').length === 0) {
            $list.html('<p class="cpv-no-variants">Nessuna variante creata per questo prodotto.</p>');
        }
    }

    /**
     * Pulisce gli errori del form
     */
    function clearFormErrors($form) {
        $form.find('.cpv-error').remove();
        $form.find('.cpv-input-error').removeClass('cpv-input-error');
    }
    
    /**
     * Reset del form
     */
    function resetForm($form) {
        // Reset manuale dei campi dato che usiamo un div invece di un form
        $form.find('input[type="text"], input[type="number"], textarea').val('');
        $form.find('.cpv-image-preview').hide();
        $form.find('.cpv-upload-image').text('Carica Immagine');
        $form.find('input[name="variant_image"]').val('');
        hideFieldError($form.find('input, textarea'));
    }
    
    /**
     * Aggiorna la lista delle varianti
     */
    function refreshVariantsList() {
        var productId = $('input[name="product_id"]').val();
        
        $.ajax({
            url: cpv_admin.ajax_url,
            type: 'POST',
            data: {
                action: 'cpv_get_variants',
                product_id: productId,
                nonce: cpv_admin.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Ricarica la pagina per semplicità
                    // In una versione più avanzata si potrebbe aggiornare solo la lista
                    location.reload();
                }
            }
        });
    }
    
    /**
     * Controlla se la lista è vuota
     */
    function checkEmptyList() {
        if ($('#cpv-variants-tbody tr').length === 0) {
            $('#cpv-variants-list').html('<p class="cpv-no-variants">Nessuna variante creata per questo prodotto.</p>');
        }
    }
    
    // Inizializza quando il documento è pronto
    init();
});
